{
    "compilerOptions": {
        "allowSyntheticDefaultImports": true,
        "baseUrl": ".",
        "outDir": "./build",
        "module": "esnext",
        "target": "es6",
        "lib": ["dom","es2015"],
        "sourceMap": true,
        "allowJs": true,
        "jsx": "react",
        "moduleResolution": "node",
        "rootDir": "src",
        "forceConsistentCasingInFileNames": true,
        "noImplicitReturns": true,
        "noImplicitThis": true,
        "noImplicitAny": false,
        "strictNullChecks": true,
        "suppressImplicitAnyIndexErrors": true,
        "noUnusedLocals": true,
        "experimentalDecorators": true,        /* Enables experimental support for ES7 decorators. */
        "emitDecoratorMetadata": true,
        "paths":{
            "commons/*": ["./src/components_common/*"],
            "tools/*": ["./src/tools/*"],
            "api/*": ["./src/api/*"],
            "config/*": ["./src/config/*"],
            "public/*": ["./public/*"],
            "scss/*": ["./src/scss_mixin/scss/*"],
            "scss_mixin/*": ["./src/scss_mixin/*"],
        }
    },
    "exclude": [
        "node_modules",
        "build",
        "config",
        "mock"
    ]
}
