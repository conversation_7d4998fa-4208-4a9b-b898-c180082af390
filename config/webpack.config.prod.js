const alias = require('./alias')
const filenames = require('./filenames')
// const externals = require('./externals')
process.env.BROWSER_ROUTER = "true";

module.exports = {

    resolve: {
        alias: alias
    },
    output: {
        publicPath: {
            js: '//j1.58cdn.com.cn/ee/benben/benben_h5',
            css: '//c.58cdn.com.cn/ee/benben/benben_h5',
            img: '//c.58cdn.com.cn/ee/benben/benben_h5',
            media: '//c.58cdn.com.cn/ee/benben/benben_h5'
        },

        filenames: filenames.prod
    },
    // externals: externals
    // cssModule: {
    //     exclude: ['src/static', 'node_modules'],
    //     name: '[name]__[local]-[hash:base64:5]'
    // }
    // plugins: isAnalyzeMode ? [new (require('webpack-bundle-analyzer').BundleAnalyzerPlugin)()] : []
}
