<!doctype html>
<html lang="zh">

<head>

    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1,minimum-scale=1.0,maximum-scale=1.0, shrink-to-fit=no,user-scalable=0,viewport-fit=cover">
    <meta name="theme-color" content="#000000">

    <title></title>
    <style>
        html {
            background: #ffffff;
            height: 100%;
        }

        body {
            position: relative;
            background: #ffffff;
            max-width: 720px;
            margin: 0 auto !important;
            height: 100%;
        }

        #root {
            height: 100%;
        }
    </style>


</head>

<body>
    <div id="root" style="height: 100%;"></div>
    <script>
        window.PointerEvent = void 0
    </script>
    <script>
            <%= htmlWebpackPlugin.options.flexibleStr %>
    </script>
    <!-- 登录 -->
    <script src="//j1.58cdn.com.cn/git/teg-app-fe/passport-sdk-m/static/js/sdk_m.js"></script>
    <!-- 埋点 -->
    <script type="text/javascript">
            window.PointerEvent = void 0;
        var hostList = ['benben.58.com', 'local.58.com:3000'];
        var host = window.location.host;
        if (hostList.indexOf(host) >= 0) {
            window.WMDA_SDK_CONFIG = ({
                api_v: 1,
                sdk_v: 0.1,
                mode: 'report',
                appid: 17419177714730000,
                key: 'ljwszpf4',
                project_id: '32942352756280',
                cate_id: '',   //按需填写
                page_extra: [{   //按需填写
                    key: '',
                    value: ''
                }],
                user_extra: [{   //按需填写
                    key: '',
                    value: ''
                }],
                channel: '',   //按需填写
                // getDeviceId: function () { },   //按需填写
                SPA_HASH: true, //单页应用必选
                getUserId: function () {
                    var keyEQ = "PPU=";
                    var itemList = document.cookie.split(';');
                    var ppuCookie = null;
                    for (var i = 0, len = itemList.length; i < len; i++) {
                        var item = itemList[i];
                        item = item.replace(/(^\s*)/g, '');
                        if (item.indexOf(keyEQ) === 0) {
                            ppuCookie = item.substring(keyEQ.length, item.length);
                        }
                    }
                    var ppuReg = /UID=(.*?)&/
                    return ppuCookie.match(ppuReg)[1];
                }
            });
            (function () {
                var wmda = document.createElement('script');
                wmda.type = 'text/javascript';
                wmda.async = true;
                wmda.src = ('https:' === document.location.protocol ? 'https://' : 'http://') + 'j1.58cdn.com.cn/wmda/js/statistic.js?' + (+new Date());
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(wmda, s);
            })();
        }
        var searchParams = window.location.search || '';
        if (searchParams && searchParams.indexOf('isExhibitLog=1') > -1) {
            var vConsoleDom = document.createElement('script');
            vConsoleDom.type = 'text/javascript';
            vConsoleDom.src = "//cdn.bootcdn.net/ajax/libs/vConsole/3.15.1/vconsole.min.js";
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(vConsoleDom, s);
            if (vConsoleDom.addEventListener) {
                vConsoleDom.addEventListener('load', function () {
                    var vConsole = new VConsole();
                }, false);
            }
        }
    </script>

</body>
<script type="text/javascript" src="//j1.58cdn.com.cn/jinrong/loan/baoxian/customer-files/aes.js"></script>
<script type="text/javascript" src="//j1.58cdn.com.cn/jinrong/loan/baoxian/customer-files/pad-zeropadding.js"></script>
<script>
    window.sqbCryptoJS = window.CryptoJS
</script>

<script type="text/javascript" src="//j1.58cdn.com.cn/jinrong/loan/baoxian/customer-files/md5.min.js"></script>
<script src="//a.58cdn.com.cn/app58/rms/app/js/app_30805.js?cachevers=620"></script>
<script src="//j1.58cdn.com.cn/jinrong/loan/baoxian/customer-files/xj-bridge-1596435317883.js"></script>
<script type="text/javascript" src="//j1.58cdn.com.cn/jinrong/daikuan/static/js/wos-js-sdk-v1.js"></script>

<!-- 新版本sdk -->
<script src="//j1.58cdn.com.cn/jinrong/loan/baoxian/customer-files/finance-app-jssdk-1601020656205.js"
    type="text/javascript"></script>
<!-- 移动端调试工具 -->
<script type="text/javascript">
    var hostName = window.location.host;
    // , 'local.58insure.com:3000'
    var testHost = ['testbaoxianm.58insure.com'];

    if (testHost.includes(hostName)) {
        document.write('<script src="//j1.58cdn.com.cn/jinrong/finsys-js/eruda.min.js"><\/script>');
        document.write('<script>eruda.init();<\/script>')
    }
</script>
<script type="text/javascript"
    src="//api.map.baidu.com/api?type=webgl&v=1.0&ak=eIcNpJ34acuOB0vktI8RhIV9rzD4g3dg"></script>

</html>