{"data": {"id": 156, "activityName": "家庭保障计划预约结果", "buttonImageUrl": "", "buttonLinkType": 3, "imageList": [{"imageLine": [{"imageUrl": "http://testv1.wos.58dns.org/hjIEdCbAZhXIQ/bxscwos/4-1.1622450829814.png", "linkUrl": "http://www.baidu.com", "needLogin": "1", "linkType": 3}]}, {"imageLine": [{"imageUrl": "http://testv1.wos.58dns.org/hjIEdCbAZhXIQ/bxscwos/2-2.1622449637885.jpg", "linkUrl": "https://m.58insure.com/agentapp/insurancemall/home?sqbChannel=58TC", "linkType": 3, "needLogin": "0"}, {"imageUrl": "http://testv1.wos.58dns.org/hjIEdCbAZhXIQ/bxscwos/2-3.1622449657346.jpg", "linkUrl": "https://m.58insure.com/agentapp/order/list", "linkType": 3, "needLogin": "1"}]}, {"imageLine": [{"imageUrl": "http://testv1.wos.58dns.org/hjIEdCbAZhXIQ/bxscwos/4-2%E5%BA%95%E9%83%A8.1622451254519.png", "linkUrl": "", "linkType": 3, "needLogin": "0"}]}]}, "rCode": 0}