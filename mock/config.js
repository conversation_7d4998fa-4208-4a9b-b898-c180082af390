const fs = require('fs');
const path = require('path');
const os = require('os');

const configList = [];
const platform = os.platform();
const reg = new RegExp(platform);

const isMac = () => {
    return reg.test('darwin');
};

const isLinux = () => {
    return reg.test('linux');
};

const isWin = () => {
    return reg.test('win32');
};

let directories = []
const files = fs.readdirSync(path.resolve(__dirname, './'));

files.forEach(file => {
    let stat = fs.lstatSync(path.resolve(__dirname, `./${file}`));
    if (stat.isDirectory() === true) {
        directories.push(file)
    }
})  
 
directories.forEach(directory => {
    fileDisplay(path.resolve(__dirname, `./${directory}`));
})

function fileDisplay(filePath){
    try {
        let files = fs.readdirSync(filePath);
        files.forEach(function(filename){
            const fileAbsolutePath = path.join(filePath, filename);
            const stats = fs.statSync(fileAbsolutePath);
            const isFile = stats.isFile();
            const isDir = stats.isDirectory();

            if (isFile) {
                let mockAddress = fileAbsolutePath.split('mock')[1];

                if (isWin()) {
                    mockAddress = mockAddress.replace(/\\/g, '/');
                }

                let param = {
                    url: mockAddress.split('.')[0],
                    type: mockAddress.includes('post') ? 'post' : 'get',
                    delay: 0,
                    dataPath: mockAddress
                };

                configList.push(param);
            }

            if (isDir) {
                fileDisplay(fileAbsolutePath);
            }
        }); 
    } catch (e) {
        throw new Error(e);
    }
 
}

function getConfigList(){
    return configList;
}

module.exports = getConfigList();
