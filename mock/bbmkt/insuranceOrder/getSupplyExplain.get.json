{"rCode": "0", "rMsg": "eddkca", "data": {"creditOrderId": "nyxsb", "id": "hcxwopzi", "userName": "lkhxyzjnv", "productName": "lguysu", "workProvinceCode": "uberexko", "workProvinceName": "yfhfn", "workCityCode": "ffxncdwat", "workCityName": "ujhdxxf", "workAreaCode": "rlrswtvkzi", "workAreaName": "kdfhdhkr", "workAddress": "qffod", "homeProvinceCode": "lqsix", "homeProvinceName": "glwvt", "homeCityCode": "nnuvykfla", "homeCityName": "ugkhd", "homeAreaCode": "pdqner", "homeAreaName": "ueipsp", "homeAddress": "nrlrs", "supplyInfo": "jazdkpzoi", "gainClientSource": "fvlmqpoilc", "supplyRemind": "vqpeaejo", "imgInfo": [{"imgUrl": "arstcn", "imgType": 52}, {"imgUrl": "ujdvcpc", "imgType": 90}, {"imgUrl": "klrhhn", "imgType": 15}]}}