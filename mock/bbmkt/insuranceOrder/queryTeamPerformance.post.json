{"rMsg": "success", "rCode": 0, "data": {"list": [{"teamId": "kljcjs", "salesId": "wquaoamn", "teamName": "nmsmmzby", "teamLeaderId": "cwmxcual", "teamLeaderName": "urggdbdq", "salesName": "satusnx", "productName": "xcamiaqh", "applicantCount": "xgeddvrry", "approvedApplicantCount": "zbhivfu", "lineOfCredit": "jtmsuyf"}, {"teamId": "kljcjs", "salesId": "wquaoamn", "teamName": "nmsmmzby", "teamLeaderId": "cwmxcual", "teamLeaderName": "urggdbdq", "salesName": "satusnx", "productName": "xcamiaqh", "applicantCount": "xgeddvrry", "approvedApplicantCount": "zbhivfu", "lineOfCredit": "jtmsuyf"}, {"teamId": "kljcjs", "salesId": "wquaoamn", "teamName": "nmsmmzby", "teamLeaderId": "cwmxcual", "teamLeaderName": "urggdbdq", "salesName": "satusnx", "productName": "xcamiaqh", "applicantCount": "xgeddvrry", "approvedApplicantCount": "zbhivfu", "lineOfCredit": "jtmsuyf"}], "total": 100, "totalApplicantCount": 100, "totalApprovedApplicantCount": 100, "totalLineOfCredit": 100, "totalLoanBalance": 100}}