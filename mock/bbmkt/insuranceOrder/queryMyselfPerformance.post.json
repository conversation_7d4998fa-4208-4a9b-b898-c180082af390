{"rMsg": "success", "rCode": 0, "data": {"list": [{"id": "amoohntd", "salesId": "ooqgkknopv", "productName": "erh<PERSON>yu", "applicantName": "a<PERSON><PERSON><PERSON>", "applyTime": "xfeaham", "applicantIdCode": "oljtmvpr", "applicantMobile": "tobjdxttxj", "auditTime": "apulwyj", "auditRemark": "qodtvgqlzd", "lineOfCredit": "s<PERSON><PERSON><PERSON>", "creditRate": "ul<PERSON>h", "creditTerm": "ggmmoekwwx", "repaymentType": "bvmgj", "loanBalance": "tftqx", "isSupplement": 0}, {"id": "amoohntd", "salesId": "ooqgkknopv", "productName": "erh<PERSON>yu", "applicantName": "a<PERSON><PERSON><PERSON>", "applyTime": "xfeaham", "applicantIdCode": "oljtmvpr", "applicantMobile": "tobjdxttxj", "auditTime": "apulwyj", "auditRemark": "qodtvgqlzd", "lineOfCredit": "s<PERSON><PERSON><PERSON>", "creditRate": "ul<PERSON>h", "creditTerm": "ggmmoekwwx", "repaymentType": "bvmgj", "loanBalance": "tftqx", "isSupplement": 1}, {"id": "amoohntd", "salesId": "ooqgkknopv", "productName": "erh<PERSON>yu", "applicantName": "a<PERSON><PERSON><PERSON>", "applyTime": "xfeaham", "applicantIdCode": "oljtmvpr", "applicantMobile": "tobjdxttxj", "auditTime": "apulwyj", "auditRemark": "qodtvgqlzd", "lineOfCredit": "s<PERSON><PERSON><PERSON>", "creditRate": "ul<PERSON>h", "creditTerm": "ggmmoekwwx", "repaymentType": "bvmgj", "loanBalance": "tftqx", "isSupplement": 1}, {"id": "amoohntd", "salesId": "ooqgkknopv", "productName": "erh<PERSON>yu", "applicantName": "a<PERSON><PERSON><PERSON>", "applyTime": "xfeaham", "applicantIdCode": "oljtmvpr", "applicantMobile": "tobjdxttxj", "auditTime": "apulwyj", "auditRemark": "qodtvgqlzd", "lineOfCredit": "s<PERSON><PERSON><PERSON>", "creditRate": "ul<PERSON>h", "creditTerm": "ggmmoekwwx", "repaymentType": "bvmgj", "loanBalance": "tftqx", "isSupplement": 0}], "total": 100, "totalApplicantCount": 100, "totalApprovedApplicantCount": 100, "totalLineOfCredit": 100, "totalLoanBalance": 100}}