{"rCode": 0, "rMsg": null, "data": {"total": 3, "list": [{"productId": "545", "productName": "太保雇主险", "isSharingPoster": 0, "headImageUrl": "https://cdntestv1.58v5.cn/hjIEdCbAZhXIQ/bxscwos/123.1678786086893.1693904353032.1695611301644.png", "productDescription": "产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点产品亮点", "productFloorPrice": "100", "productFloorPriceUnit": "100", "productLinkUrl": "http://testbaoxianm.58insure.com/agentapp/partner_productmarket/home/<USER>", "productLinkType": 1, "productAttributeList": [{"title": "58数科旗下帮帮保险销售联合泰康在线提供企业保险定制解决方案", "description": "保障企业的保障计划"}]}, {"productId": "561", "isSharingPoster": 1, "productName": "非机动车三者责任险", "headImageUrl": "https://cdntestv1.58v5.cn/hjIEdCbAZhXIQ/bxscwos/asdfghjkl.1701657794948.jpg", "productDescription": "高性价比，为上牌车主提供保险；物损人伤  全面保障。", "productFloorPrice": "60", "productFloorPriceUnit": "元", "productLinkUrl": "http://testbaoxianm.58insure.com/agentapp/partner_productmarket/home/<USER>", "productLinkType": 2, "productAttributeList": [{"title": "保障全", "description": "物损人伤"}]}, {"productId": "600", "isSharingPoster": 0, "productName": "测试-小程序跳转", "headImageUrl": "https://cdntestv1.58v5.cn/hjIEdCbAZhXIQ/bxscwos/1712833547145-%E5%B0%8F%E7%A8%8B%E5%BA%8F%E4%BA%8C%E7%BB%B4%E7%A0%81.1724902584971.jpg", "productDescription": "test", "productFloorPrice": "100", "productFloorPriceUnit": "10", "productLinkUrl": "weixin://dl/business/?appid=wx20a578b820f04c9c&path=pages/addPlanner/addPlanner", "productLinkType": 2, "productAttributeList": [{"title": "test", "description": "好"}, {"title": "2", "description": "23"}]}]}}