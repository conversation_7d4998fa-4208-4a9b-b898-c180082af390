{"rCode": 0, "rMsg": null, "data": [{"teamProductId": "545", "teamProductName": "545-太保雇主险", "productCategoryEnum": 0, "productAndTypeId": "0-545"}, {"teamProductId": "576", "teamProductName": "576-太健康.百万全家桶", "productCategoryEnum": 0, "productAndTypeId": "0-576"}, {"teamProductId": "552", "teamProductName": "552-众安保存", "productCategoryEnum": 0, "productAndTypeId": "0-552"}, {"teamProductId": "561", "teamProductName": "561-非机动车三者责任险", "productCategoryEnum": 0, "productAndTypeId": "0-561"}, {"teamProductId": "551", "teamProductName": "551-“安燃无忧”燃气综合险", "productCategoryEnum": 0, "productAndTypeId": "0-551"}, {"teamProductId": "595", "teamProductName": "595-甜蜜家2023", "productCategoryEnum": 0, "productAndTypeId": "0-595"}, {"teamProductId": "546", "teamProductName": "546-太保团意险", "productCategoryEnum": 0, "productAndTypeId": "0-546"}, {"teamProductId": "1", "teamProductName": "1-好借001", "productCategoryEnum": 1, "productAndTypeId": "1-1"}, {"teamProductId": "2", "teamProductName": "2-好借002", "productCategoryEnum": 1, "productAndTypeId": "1-2"}, {"teamProductId": "3", "teamProductName": "3-骨头汤", "productCategoryEnum": 1, "productAndTypeId": "1-3"}, {"teamProductId": "8", "teamProductName": "8-berber产品", "productCategoryEnum": 1, "productAndTypeId": "1-8"}, {"teamProductId": "11", "teamProductName": "11-berber产品", "productCategoryEnum": 1, "productAndTypeId": "1-11"}]}