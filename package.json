{"name": "benben_h5", "version": "0.1.0", "private": true, "author": {"name": "zuoguangcheng", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "http://gitlab.xxx.com/groups/FFF-team/insuranceMall_h5"}, "group": "FFF-team", "type": "react-ts", "keywords": ["earth", "react", "insuranceMall_h5"], "dependencies": {"@sqb/utility": "^1.0.13", "antd-mobile-v5": "npm:antd-mobile@^5.32.0", "bx-ui-mobile": "^1.0.50", "classnames": "^2.2.6", "compressorjs": "^1.2.1", "core-js": "^3.6.5", "cross-env": "^7.0.2", "heic2any": "0.0.4", "history": "^5.0.0", "hybrid": "^2.0.4", "jr_wmda_report": "^2.1.1", "jsonp-p": "^2.0.0", "lm-loading": "^0.3.1", "lodash": "^4.17.19", "moment": "^2.27.0", "prop-types": "^15.7.2", "pulltorefreshjs": "^0.1.20", "query-string": "^6.13.1", "raf": "^3.4.1", "react": "16.13.1", "react-dom": "16.13.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-use": "^15.3.3", "rrweb": "^0.9.14", "swiper": "^6.4.5", "typescript": "^3.9.7", "url-search-params-polyfill": "^8.1.0", "wmda_bx_report": "0.0.3"}, "scripts": {"start": "cross-env REACT_APP_BUILD_TYPE=dev earth-scripts start", "start:test": "cross-env REACT_APP_BUILD_TYPE=QA_RELEASE earth-scripts start", "start:rd": "cross-env REACT_APP_BUILD_TYPE=rd earth-scripts start", "build": "earth-scripts build", "build:test": "cross-env REACT_APP_BUILD_TYPE=test earth-scripts build", "update": "earth-scripts update", "test": "earth-scripts test --env=jsdom", "mock": "earth-scripts mock"}, "eslintConfig": {"extends": "react-app"}, "devDependencies": {"@types/classnames": "^2.2.10", "@types/lodash": "^4.14.158", "@types/react": "^16.9.43", "@types/react-dom": "^16.9.8", "@types/react-router": "^5.1.8", "@types/react-router-dom": "^5.1.5", "@types/whatwg-fetch": "0.0.33", "babel-plugin-import": "^1.13.0", "babel-plugin-try-catch-auto": "^2.4.6", "earth-scripts": "^3.0.1"}, "proxy=>moved-to-/src/setupProxy.js": "http://127.0.0.1:3001"}