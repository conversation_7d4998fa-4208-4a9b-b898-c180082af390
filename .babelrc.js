/**
 * 替换掉原来的.babelrc文件
 * js配置更具有灵活性。比如根据不同的环境babel可以有不同的配置
 */

module.exports = {
    presets: [["@babel/preset-env"], "react-app"],
    plugins: [
        ["import", { libraryName: "antd-mobile", style: "css" }, "antd-mobile"],
        ["import", { libraryName: "bx-ui-mobile" }, "bx-ui-mobile"],
        [
            "import",
            {
                libraryName: "antd-mobile-v5",
                libraryDirectory: "es/components",
                style: false,
            },
        ],
        [
            "import",
            {
                libraryName: "react-use",
                libraryDirectory: "lib",
                camel2DashComponentName: false,
            },
            "react-use",
        ],
        ["try-catch-auto"],
    ],
};
