{
    "defaultSeverity": "warning",
    "extends": [
        "tslint:recommended",
        "tslint-react",
        "tslint-config-prettier"
    ],
    "jsRules": {},
    "rules": {
        // http://eslint.org/docs/rules/
        // https://cloud.tencent.com/developer/chapter/12618
        //必须使用分号
        "semi": ["error", "always"],
        "semi-spacing": "error",
        "space-before-blocks": ["error", { "functions": "never", "keywords": "always", "classes": "always" }],
        "space-before-function-paren": ["error", "never"],
        "space-infix-ops": "error",
        // 该规则强制关键字和类似关键字的符号周围空格的一致性
        "keyword-spacing": ["error", { "before": true, "after": true }],
        // "indent": ["error", "tab"],
        "words": 0,
        "nonwords": 0,
        //强制数组方法的回调函数中有 return 语句
        "array-callback-return": "off",
        // 要求 switch 语句中有 default 分支
        "default-case": ["warn", { "commentPattern": "^no default$" }],
        //要求使用 === 和 !==
        "eqeqeq": ["off", "smart"],
        //要求调用无参构造函数时有圆括号
        "new-parens": "warn",
        //禁用 Array 构造函数
        "no-array-constructor": "warn",
        //禁用 arguments.caller 或 arguments.callee
        "no-caller": "warn",
        //禁止条件表达式中出现赋值操作符
        "no-cond-assign": ["warn", "except-parens"],
        //禁止修改 const 声明的变量
        "no-const-assign": "warn",
        //禁止在正则表达式中使用控制字符
        "no-control-regex": "warn",
        //禁止 function 定义中出现重名参数
        "no-dupe-args": "warn",
        //禁止类成员中出现重复的名称
        "no-dupe-class-members": "warn",
        //禁止出现重复的 case 标签
        "no-duplicate-case": "warn",
        //禁止使用空解构模式
        "no-empty-pattern": "warn",
        //禁止对 catch 子句的参数重新赋值
        "no-ex-assign": "warn",
        //禁止 case 语句落空
        "no-fallthrough": "warn",
        //禁止对 function 声明重新赋值
        "no-func-assign": "warn",
        //禁止对原生对象或只读的全局对象进行赋值
        "no-global-assign": "warn",
        //禁止对关系运算符的左操作数使用否定操作符
        "no-unsafe-negation": "warn",
        //禁用 Object 的构造函数
        "no-new-object": "warn",
        //禁止 Symbolnew 操作符和 new 一起使用
        "no-new-symbol": "warn",
        //禁止对 String，Number 和 Boolean 使用 new 操作符
        "no-new-wrappers": "warn",
        //禁用八进制字面量
        "no-octal": "warn",
        //禁止在字符串中使用八进制转义序列
        "no-octal-escape": "warn",
        //禁止正则表达式字面量中出现多个空格
        "no-regex-spaces": "warn",
        //禁止使用 javascript: url
        "no-script-url": "off",
        //禁用未声明的变量，除非它们在 /*global */ 注释中被提到
        "no-undef": "warn",
        // "no-unreachable": "warn",
        //禁止出现未使用过的表达式
        "no-unused-expressions": [
            "warn",
            {
                "allowShortCircuit": true,
                "allowTernary": true,
                "allowTaggedTemplates": true
            }
        ],
        // 	禁用出现未使用过的标
        "no-unused-labels": "warn",
        // 禁止在变量定义之前使用它们
        "no-use-before-define": [
            "warn",
            {
                "functions": false,
                "classes": false,
                "variables": false
            }
        ],
        "object-curly-spacing":["error", "always"]
    },
    "rulesDirectory": ["app", "config"]
}
