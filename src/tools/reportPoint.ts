import { reportPoint as wmdaReport, WmdaReportInit } from 'jr_wmda_report';
import { isProduction } from "@sqb/utility";

const reportPoint = (data, immediate?) => {
    if (isProduction) {
        !immediate ? wmdaReport(data) : wmdaReport(data, immediate);
        return;
    } else {
        wmdaReport(data);
    }
    // console.table([data]);
}

// 背景：保险商城首页 页面曝光埋点数据明显少于异步请求数据返回后需要展示的模块曝光数据量 分析是页面js加载时间短，wmda sdk加载时间长导致，页面曝光埋点时候调用wmda的方法失败导致的，
// 做了多次ab测对比数据，包括setTimeout设置0 300 500 700 1000不同的延迟时间，window上找不到方法的时候轮询三次等方法观测数据 最终决定使用数据差异最少的300毫秒来解决首页曝光量少的问题
// 一般用于项目加载后首页曝光埋点
const reportPointAsync = data => {
    // if (isProduction) {
        setTimeout(() => {
            wmdaReport(data);
        }, 300);
        return;
    // }
    // console.table([data]);
}

export {
    reportPoint,
    WmdaReportInit,
    reportPointAsync,
}