import { API } from 'src/api/constants';
import request from "api/request";
import { Toast } from 'antd-mobile-v5'
import { locStorage } from '@sqb/utility';

console.log(process.env.REACT_APP_BUILD_TYPE === "test", process.env.REACT_APP_BUILD_TYPE, "process.env.REACT_APP_BUILD_TYPE");

// 是否测试环境
const isTest = process.env.REACT_APP_BUILD_TYPE === "test" || process.env.REACT_APP_BUILD_TYPE === "rd";
/**
 * test conf
 */
let appid = 'giHLcBazYxgJL';
let sid = 'hgNxM3qbynatAWVU4tr4BliN7GTpLHQW';
let wosurl = '//wostest215.58v5.cn';
let bucket = 'test';
if (window.location.hostname === 'benben.58.com' && !isTest) {
    /**
     * online conf
     */
    appid = 'oqmLkJiHoPwT';
    sid = 'HpTj00dbs1eCxS5AyGVvbCuUGM6MPWoL';
    wosurl = 'https://wos20.58.com';
    bucket = "sales"
}


//文件过期时间，0为不设置，单位为小时，最小值168（7天）
const ttl = 0;

//insertOnly==0 表示允许覆盖文件 1表示不允许覆盖
const insertOnly = 0;

//大文件上传的分片大小，默认1M，支持1,2,3,4M,小文件该字段为0，不受该值影响.
const sliceSize = 1024 * 1024;

let taskId = '';


let toastContent = '图片上传中...';

function getWosInstanceFactory(filename) {
    let wos: any = {};

    if (window.WosSys && window.WosSys !== wos.constructor) {

        // @ts-ignore
        wos = new WosSys({
            appid: appid,// APPID 必填参数
            bucket: bucket,//bucketName 必填参数
            wosurl: wosurl,//wos的url 必填参数
            getAppSign: function (callback) {//获取签名 必填参数
                const params = {
                    fileName: filename
                }
                const url = API.GET_WOS_TOKEN;
                request.get(url, params).then((res) => {
                    if (res?.rCode === 0) {
                        const sig = res.data;
                        callback(encodeURIComponent(sig));
                        return
                    }
                    // callback(encodeURIComponent(""));
                    Toast.show("上传失败")

                }).catch((e) => {
                    // 请求token错误 图片组建捕捉不到 因此这里返回一个错误的token
                    console.log(e, "err");
                    callback(encodeURIComponent(""));
                })

            }

        });

    }
    return wos;
}






const _taskReady = (_taskId) => {
    console.log(_taskId, "_taskId");

    taskId = _taskId;
}

const _progCb = (a) => {
    console.log(
        a,
        "process"
    );

    // Toast.show({
    //     content: toastContent,
    //     duration: 8000,
    //     position: 'top',
    //     maskClickable: false
    // })
}


//不区分大小文件，sdk内部自动实现分片上传
const uploadPic = (filename, file, loadingText = toastContent, progCb = _progCb) => {
    toastContent = loadingText;
    let wos = getWosInstanceFactory(filename)
    return new Promise((resolve, reject) => {
        return wos.uploadFile((result) => {
            resolve(JSON.parse(result));
        }, (result) => {
            console.log("wos upload fail")
            Toast.show({
                content: '图片上传失败，请重新上传',
                duration: 3000,
                position: 'top',
                maskClickable: false
            })
            reject(result);
        }, progCb, bucket, filename, file, ttl, insertOnly, _taskReady, sliceSize, '');
    });
}



export {
    uploadPic,
}
