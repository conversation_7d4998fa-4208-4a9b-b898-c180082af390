/**
 * 手动上报埋点。
 * 支持未上传的埋点进行localStorage存储下次进入后进行上传
 *
 * */

import { useMount, useUnmount } from "react-use";
import { isNaN, isNumber } from "lodash";

const usePoint = (props) => {
    let timer: any = null;
    let times: number = 0;
    const point = props.point;

    useMount(() => {
        reportPoint(point);

        // 获取localStorage中的埋点信息
        const OldPoint = window.localStorage.getItem('point');

        try {
            //如果存在，则上报埋点信息
            !!OldPoint && reportPoint(JSON.parse(OldPoint));
        }catch (e) {
            // window.JSTracker && (
            //     window.JSTracker.catch({
            //         message: OldPoint, stack: e.stack.toString()
            //     })
            // )
        }
    })

    useUnmount(() => {
        clearTimeout(timer);
    })

    const sendPoint = (point) => {
        if(point) {

            // 判断如果是number类型， 需要拼接event_id, 否则直接发送
            if(isNumber(Number(point)) && !isNaN(Number(point))) {
                window.WMDA_REPORT('custom', { event_id: point });
            }else {
                window.WMDA_REPORT('custom', point);
            }
        }
    }

    const reportPoint = (point: object | []) => {
        times++;

        // 如果不存在，证明sdk未加载成功，需要1s轮询一次， 暂定8次后存入localStorage中，以便下次进入可以重新发送
        if(!window.WMDA_REPORT) {

            timer = setTimeout(() => {
                if(times <= 8) {
                    reportPoint(point);
                } else {

                    try {
                        // 获取 localStorage中 point
                        const pointString = window.localStorage.getItem('point');

                        if(!!pointString) {
                            let pointOld = JSON.parse(pointString);

                            // localStorage中数据应该是一个数组
                            if(Array.isArray(pointOld)) {

                                // 如果当前传入进来的是数组，则拼接数组，否则， 直接push进来
                                // 从localStorage中获取的数据是数组。
                                if(Array.isArray(point)) {
                                    pointOld.concat(point);
                                } else {
                                    pointOld.push(point);
                                }

                                // 重新存入localStorage
                                window.localStorage.setItem('point', JSON.stringify(pointOld));
                            }

                        }else {
                            // 如果localStorage中不存在point，则重新设置一个数组
                            window.localStorage.setItem('point', JSON.stringify([props.point]));
                        }
                    }catch (e) {
                        // window.JSTracker && (
                        //     window.JSTracker.catch({
                        //         message: point, stack: e.stack.toString()
                        //     })
                        // )
                    }
                }

            }, 1000);
        } else {
            if(Array.isArray(point)) {
                // 如果是array，则证明是从localStorage中获取的， 因此需要删除
                window.localStorage.removeItem('point');

                point.forEach(item => sendPoint(item));
            }else {
                sendPoint(point);
            }
        }
    }

}

export default usePoint;
