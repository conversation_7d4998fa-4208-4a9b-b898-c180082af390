/**
 * 页面路由增加login逻辑判断，如果页面链接配置参数need_login=1, 则需要进行登录校验
 *
 * */
import { useMount } from "react-use";

// import { getRequestParams } from 'tools/utils';
import { loginFn, getRequestParams } from "@sqb/utility";
import fetch from "api/request";
import API from "api/const";

const usePageLogin = (props?: any) => {
    useMount(async () => {
        const need_login = getRequestParams(window.location.search).need_login;
        const needLogin = getRequestParams(window.location.search).needLogin;
        loginFn();

        // 页面路径如果有isLogin 并且值为1， 则页面需要进行登录操作
        if (need_login === "1" || needLogin === "1") {
            try {
                const res = await fetch.get(API.GET_GET_LOGIN_STATE);
                const { rCode, data } = res;
                if (rCode === 0) {
                    if (Number(data.state) === 1) {
                        //已登录
                    } else {
                        loginFn();
                    }
                }
            } catch (error) {
                console.log(error);
            }
        }
    });
};

export default usePageLogin;
