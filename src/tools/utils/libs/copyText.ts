import Toast from "antd-mobile-v5/es/components/toast";

const copyByExecCommand = (text: string, tips: string) => {
    // 使用 input 元素作为后备方法
    let input = document.createElement('input');
    input.readOnly = true;
    input.value = text;
    document.body.appendChild(input);
    input.focus();
    input.select();

    try {
        const isSuccess = document.execCommand('copy');
        Toast.show(!isSuccess? '复制失败，请重试！' : tips);
    } catch (err) {
        Toast.show('复制失败，请重试！');
    }
    document.body.removeChild(input);
}

export const copyText = (text: string, tips: string = '复制成功') => {
    if (navigator.clipboard && navigator.clipboard.writeText && window.isSecureContext) {
        // 使用 Clipboard API
        navigator.clipboard.writeText(text)
            .then(res => {
                Toast.show(tips);
            })
            .catch(err => {
                copyByExecCommand(text, tips);
            });
    } else {
        copyByExecCommand(text, tips);
    }
}
