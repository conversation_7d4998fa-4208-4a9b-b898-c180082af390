import { isNaN, isNumber } from 'lodash';
import { useMount } from 'react-use';


/**
 * 点击埋点自动上报，使用监听body dom方案
 * 使用方式：
 *
 * 1.只上报埋点值，无额外字段
 * <Button className="submit" data-clickpoint={1988} onClick={onSubmit}>免费领取</Button>
 * 增加自定义属性 data-clickpoint即可
 *
 * 2.需要额外上报字段
 * <Button className="submit" data-clickpoint={JSON.stringify({event_id: "444", key1: 'key1', key2: 'key2'})} onClick={onSubmit}>免费领取</Button>
 * 注意属性要使用JSON.stringify()进行字符串化
 *
 * */
const clickPoint = () => {
    const root: Node = document.getElementsByTagName('body')[0];

    const getParentNode = ((target: Element) => {
        try {
            const clickPoint: string = target.getAttribute('data-clickPoint') || "";

            if(!clickPoint && target.tagName !== "BODY") {
                getParentNode((target.parentNode as Element));
            } else {
                if(isNumber(Number(clickPoint)) && !isNaN(Number(clickPoint))) {
                    window.WMDA_REPORT && window.WMDA_REPORT('custom', { event_id: clickPoint });
                } else {
                    window.WMDA_REPORT && window.WMDA_REPORT('custom', JSON.parse(clickPoint));
                }
            }
        }catch (e) {
            console.error('埋点上传错误', e);
        }
    })

    root.addEventListener('click', function (e:Event) {
        getParentNode((e.target as Element))
    });
}


/**
 * 使用react hooks 在组件mount时触发
 * 使用方式
 *
 * 在页面跟组件中引入该hooks
 *
 * import { useMountPoint } from 'tools/utils/libs/buryingPoint'
 *
 * 1.只上传埋点值，无额外字段
 * useMountPoint(19900)
 *
 * 2.需要上传额外字段
 *
 * useMountPoint({
 *      event_id: "909090",
 *      key1: 'key1',
 *      key2: 'key2'
 *  });
 *
 * */
const useMountPoint = (mountPoint: number | object) => {
    useMount(() => {
        try {
            if(isNumber(mountPoint) && !isNaN(Number(mountPoint))) {
                window.WMDA_REPORT('custom', { event_id: mountPoint });
            }else {
                window.WMDA_REPORT('custom', mountPoint);
            }
        }catch (e) {
            // window.JSTracker && (
            //     window.JSTracker.catch({
            //         message: mountPoint, stack: e.stack.toString()
            //     })
            // )
        }

    })
}

export {
    clickPoint,
    useMountPoint,
}
