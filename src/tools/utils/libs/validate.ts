/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-01-19 21:33:02
 * @LastEditors: zhangwu01 <EMAIL>
 * @LastEditTime: 2024-01-19 21:35:17
 * @Description: 表单正则校验
 * 
 */

// 第一位1 第二位 3-9  共11位 （大陆手机号）
export const phoneExp = /^1[3-9]\d{9}$/;

// 银行卡号 16-19位
export const bankNoExp = /^\d{16,19}$/

// 手机号中间四位脱敏 用**** 替换
export function maskPhoneNumber(phone: string): string {
    return phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1 **** $2');
}

/**
 * 校验通过 xxx **** xxxx 和 xxxxxxxxxxx 的手机号
 *
 * @param phoneNumber 手机号码，可以是隐藏中间四位的格式（xxx **** xxxx）或完整连续的11位数字（xxxxxxxxxxx）
 * @returns 如果是有效手机号码返回 true，否则返回 false
 */
export function isValidPhoneNumber(phoneNumber: string) {
    if (!phoneNumber) return false;

    // 对于隐藏格式，检查前后可见部分是否符合已知的手机号码前缀规则
    if (/^\d{3}\s\*\*\*\*\s\d{4}$/.test(phoneNumber)) {
        return true;
    }

    // 对于完整手机号码，直接使用手机号码正则表达式进行验证
    return phoneExp.test(phoneNumber);
}