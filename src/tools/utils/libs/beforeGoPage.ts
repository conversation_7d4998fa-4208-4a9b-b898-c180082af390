import { isFunction } from 'lodash';
import request from 'api/request';
import API from 'api/const';

//是否需要登录
const needLogin = (url: string) => {
    return /needLogin=1/.test(url);
};
//跳转需要前置登录
export const beforeGoPage = async(url: string, cb: ()=> void) => {
    try {
        if (!url) return;
        // 如果需要登录
        if (needLogin(url)) {
            const res = await request.get(API.GET_LOGIN_REGISTER);
            if (res.rCode === 0 || (res.rCode === 2 && res.isLogin)) { //已登录或者未登录后登录成功
                isFunction(cb) && cb();
            }
        } else {
            // 如果不需要登录
            isFunction(cb) && cb();
        }
    } catch (error) {
        console.error(error);
    }
};
//跳转需要前置登录, return Promise
export async function beforeGoPagePromise(url: string): Promise<any> {
    try {
        if (!url) return {};
        // 如果需要登录
        if (needLogin(url)) {
            const res = await request.get(API.GET_LOGIN_REGISTER);
            return res;
        } else {
            // 如果不需要登录
            return { needLogin: false };
        }
    } catch (error) {
        console.log(error);
        return error;
    }
};