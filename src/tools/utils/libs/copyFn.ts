import Toast from "antd-mobile-v5/es/components/toast";

export async function copyFn(android: string, tips = "复制成功") {
    //有剪切版，使用剪切板
    if (navigator.clipboard) {
        //IOS
        const text = document.getElementById(android)?.getAttribute("value"); //要复制文字的节点
        await navigator.clipboard
            .writeText(text || "")
            .then((res) => {
                Toast.show(tips);
                return;
            })
            .catch((e) => {
                console.log(e);
            });
    }
    //无剪切版，使用execCommand
    const Url2 = document.getElementById(android); //要复制文字的节点
    Url2 && (Url2 as HTMLInputElement).select(); // 选择对象
    const successful = document.execCommand("Copy"); // 执行浏览器复制命令
    if (successful) {
        Toast.show(tips);
    }
}
