// 是否是测试环境和本地
export const isTest = () => {
    var hostList = ['testbaoxianm.58insure.com', 'localhost:3000'];
    var host = window.location.host;
    if (hostList.indexOf(host) >= 0) {
        return true;
    } else {
        return false;
    }
};

const originKey = "c7c17e9bc1625fbf";

const originIv = "4171fc592b49eb30";

const decryptedKey = () => {
    let key = window.sqbCryptoJS.enc.Latin1.parse(originKey);
    let iv = window.sqbCryptoJS.enc.Latin1.parse(originIv);
    return {
        key,
        iv
    };
};

function dataToString (data: any) {
    if (!data) return '';

    if (typeof data === 'object') {
        return JSON.stringify(data);
    }

    return `${data}`;
}

export const encrypted = (data) => {
    let keyValue = decryptedKey();
    //加密

    return window.sqbCryptoJS.AES.encrypt(dataToString(data), keyValue.key, {
        iv: keyValue.iv,
        mode: window.sqbCryptoJS.mode.CBC,
        padding: window.sqbCryptoJS.pad.ZeroPadding
    });
};

export const decrypted = (data) => {
    if (data === null) {
        return null;
    }
    isTest() && console.log(data.replace(/[\r\n]/g,""));
    let keyValue = decryptedKey();
    // isTest() && console.log('keyValue', keyValue);
    let decrypted = window.sqbCryptoJS.AES.decrypt(data.replace(/[\r\n]/g,""), keyValue.key, { iv: keyValue.iv, padding: window.sqbCryptoJS.pad.ZeroPadding });
    const result = decrypted.toString(window.sqbCryptoJS.enc.Utf8);
    isTest() && console.log('-----', result);

    if (/^[{|\[].*[}|\]]$/.test(result)) {
        return JSON.parse(result);
    }
    return result;
};

window.decrypted = decrypted;
