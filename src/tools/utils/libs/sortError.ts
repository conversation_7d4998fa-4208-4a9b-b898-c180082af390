/**
 * 按照arr数组里面的顺序，返回第一条错误信息；
 * @param err：错误信息集合
 * @param arr：id集合，按该顺序提示错误
 * @return string：错误信息
*/
export const sortErrorMsg = (err = {},arr: string[] = []) =>{
    let result = '';
    for (let i of arr) {
        if (!result && err[i]) {
            let errMsg = err[i].errors;
            let tipMsg = errMsg[errMsg.length - 1].message;
            result = tipMsg;
        }
    }
    return result;
};