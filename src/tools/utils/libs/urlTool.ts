/**
 * url拼接search参数
 * @param url string
 * @param params object 要拼接的参数key value对象
 * @return 拼接后的url
 */
interface IKVItem {
    [propName: string]: string;
}

export const concatParams = (url: string, params: IKVItem) => {
    function param(data) {
        let url = '';
        // 遍历data对象，取出需要的参数
        for (var k in data) {
            // 如果当前value为undefined ，则返回空字符串
            let value = data[k] !== undefined ? data[k] : '';
            // 得到参数，并且拼接参数，为下一步拼接到url后面做准备
            url += '&' + k + '=' + encodeURIComponent(value);
        }
        // 如果url存在，则去除首字符并返回，因为主函数已经包含了'&'，否则返回空串
        return url ? url.substring(1) : '';
    }
    try {
        // 拼接url
        return (url += (url.indexOf('?') < 0 ? '?' : '&') + param(params));
    } catch (error) {
        console.log('concat params err', error);
        return '';
    }
};
