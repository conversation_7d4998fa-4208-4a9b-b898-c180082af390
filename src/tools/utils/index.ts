import { getCookie } from './libs/cookie';
import _, { transform } from 'lodash';
import { Toast } from "antd-mobile";

/**
 * 截出字符串
 * @param search
 * @param reg
 */
interface ImatchSearch {
    (search: any, reg: RegExp): any
}
const matchSearch: ImatchSearch = (search, reg) => search && search.match(reg) && search.match(reg)[1] ? search.match(reg)[1] : null;

/**
 * 配合fetch 格式化body
 * @param params
 */
const stringifyParams = (params: object) => (
    Object.keys(params).map((key: string): string => (key + '=' + encodeURIComponent(params[key]))).join('&')
);

/**
 * 判断是否是ios
 * @returns {boolean}
 */
const isIos: () => boolean = () => {
    return /(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)
};


/**
 * 判断是否是58app
 */
const is58App: () => boolean = () => {
    return /WUBA/i.test(navigator.userAgent) || getCookie('58ua') === '"58app"'
};



/**
 * 判断是否是好借app
 * */
const isHaojie: () => boolean = () => {
    return (/JRHaoJie/i).test(window.navigator.userAgent);
}

/**
 * 判断是否是微信
 */
const isWechat: () => boolean = () => {
    return (/micromessenger/i).test(window.navigator.userAgent.toLowerCase());
};


/**
 * 判断是否是神奇保APP
 */
const isShenQiBao = () => {
    return (/shenqibaoAPP/i).test(window.navigator.userAgent);
};


const getAbsoultePath: (href: string) => string = href => {
    let link = document.createElement('a');
    link.href = href;
    return (link.protocol + '//' + link.host + link.pathname + link.search + link.hash);
};


/**
 * 监听浏览器回退事件
 * @param actionToDo
 */
// @ts-ignore
type ACTIONTODO = (e: Event) => void
const pageBackFromNextPage = (actionToDo: ACTIONTODO): void => {

    // pageshow
    // UA.android && window.addEventListener('focus', actionToDo, false);
    window.addEventListener('pageshow', function (e) {
        if (e.persisted) {
            actionToDo(e);
        }
    }, false);

    // visibilityChange
    document.addEventListener('visibilitychange', function (e) {
        if (document.visibilityState === 'visible' || !document.hidden) {
            actionToDo(e);
        }
    }, false);

    // webkitVisibilityChange
    document.addEventListener('webkitVisibilitychange', function (e) {
        // @ts-ignore
        if (document.webkitVisibilityState === 'visible' || !document.webkitHidden) {
            actionToDo(e);
        }
    }, false);
};

const isDevelopment = process.env.NODE_ENV === 'development';
const isBuildDevelopment = process.env.REACT_APP_BUILD_TYPE === 'QA_RELEASE';

/**
 * 获取URL 参数对象
 * @param query
 * @returns {{}}
 */
interface IgetRequestParams {
    (query: string): { [propName: string]: any }
}
const getRequestParams: IgetRequestParams = (query) => {
    let search = query.trim().replace(/^[?#&]/, '') || window.location.search.substring(1);
    return search ? JSON.parse('{"' + search.replace(/&/g, '","').replace(/=/g, '":"') + '"}', function (key, value) {
        return key === "" ? value : decodeURIComponent(value);
    }) : {};
};


const uuidv4 = function () {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};


function urlAddSearch(url: string) {
    let newUrl = '';
    const search = window.location.search.substr(1);
    if (!search) return url;
    if (url.match("[\?]")) {
        newUrl = url + "&" + search;
    }
    else {
        newUrl = url + "?" + search;
    }
    return newUrl;
}


/**
 *
 * @param {String} 需要改变的url
 * @param {String} 需要增加的key
 * @param {String} 需要设置的key的value 值
 *
 * */
function changeParam(url, name, value) {
    if (!url) {
        return '';
    }

    let newUrl = "";
    const reg = new RegExp("(^|)" + name + "=([^&]*)(|$)");
    let param = name + "=" + value;

    if (url.match(reg) != null) {
        newUrl = url.replace(reg, param);
    } else {
        if (url.match("[\?]")) {
            newUrl = url + "&" + param;
        }
        else {
            newUrl = url + "?" + param;
        }
    }

    return newUrl;
}

/**
 * @param {String} url 需要匹配的url
 * @param {Object} replaceValue 需要替换的字段的key value对应  key userId | mallSource | mallResource
 *
 * */
function replaceUrl(url: string, replaceValue: object = {}) {
    const regAll = /\$\{(.*?)\}/g;
    const regItem = /\$\{(.*?)\}/;
    const match = url.match(regAll);

    let newUrl = url;

    match?.forEach(item => {
        const key = (item.match(regItem));

        if (key) {
            const re = new RegExp('\\$\\{' + key[1] + '\\}', "g");
            newUrl = newUrl.replace(re, replaceValue[key[1]]);
        }
    });

    return newUrl;
}

const isLoginWechat = getRequestParams(window.location.search).need_login_wechat;

const defaultConfig = {
    defaulttype: 2, // 默认展示的登录方式：1.默认展示账号密码登录  2.默认展示手机号动态码登录
    ishidethird: isLoginWechat === '1' ? 0 : 1, // 是否隐藏第三方登录方式：   ishidethird=1即可隐藏
    ishidetpage: 1, // 是否隐藏左上角首页链接：   ishidetpage=1即可隐藏
};
const loginUrl = `${window.location.protocol}//passport.58.com/m/login`;
const getCurPath = (pathName) => {
    if (pathName) {
        return window.location.href.replace(window.location.pathname, `${pathName}`);
    } else {
        return window.location.href;
    }
};
const getPassportUrl = (pathname: string, customerConfig?: object) => {
    let path = getCurPath(pathname);
    let config = customerConfig || defaultConfig;
    if (_.isPlainObject(config)) {
        let str = '';
        Object.keys(config).forEach((key) => {
            str += `&${key}=${config[key]}`;
        });
        return `${loginUrl}?path=${encodeURIComponent(path)}&source=58-insurem-m${str}&isredirect=true`;
    } else {
        return `${loginUrl}?path=${encodeURIComponent(path)}&source=58-insurem-m&isredirect=true`;
    }
};

/**
 * 监听ios测滑 58同城
 * @param callback
 */
const sideTouchHandler = callback => {
    if (is58App() && isIos()) {
        window.WBAPP.invoke('toggle_gesture', {
            cmd: 'off',
            edge_paning: true,
            webview_paning: 'off'
        }, callback);
    }
}

// 监听返回 只有同城app和好借sdk有device_event这个方法 58好借没有哦
const listenBack = callback => {
    if (is58App()) {
        window.WBAPP && window.WBAPP.invoke('device_event', {
            type: 'goback', // goback:页面回退时触发；pageshow:页面展示时触发
        }, callback);
    }
}

const resetGoBack = (callback) => {
    if (is58App()) {
        window.WBAPP && window.WBAPP.invoke('device_event', {
            type: 'goback', // goback:页面回退时触发；pageshow:页面展示时触发
        }, callback);
    }
}

/**
 * 获取URL 参数对象格式化处理协议  把http: https:去掉
 * @param website
 * @returns string
 */
const formatProtocol = (website?: string): string => {
    if (!website) {
        return '';
    }

    return website.replace(/^(http|https)\:/, '');
};
export const transformData = (data = [], codeKey = "code", descKey = "desc") => {
    return data.map(item => ({
        value: item[codeKey],
        label: item[descKey]
    }));
}

export function desensitizePhoneNumber(phoneNumber) {
    if (phoneNumber.length === 11) {
        return phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(7);
    } else {
        // 可以根据实际情况处理非标准的手机号格式
        return phoneNumber;
    }
}
// 以出url中的某个参数
export function removeQueryParameter(url: string, parameterName: string) {
    // 输入验证：确保url是字符串且parameterName是合法的URL参数名
    if (typeof url !== 'string' || !url.trim()) {
        throw new Error('Invalid URL.');
    }
    if (typeof parameterName !== 'string' || !parameterName.trim()) {
        throw new Error('Invalid parameter name.');
    }
    try {
        // 创建URL对象
        const urlObj = new URL(url);

        // 移除指定的查询参数
        urlObj.searchParams.delete(parameterName);

        // 返回修改后的URL字符串
        return decodeURIComponent(urlObj.toString());
    } catch (e) {
        console.log(e);

    }

}

async function geolocationSuccess(r, resolve) {
    try {
        //@ts-ignore
        if (this.getStatus() == BMAP_STATUS_SUCCESS) {
            console.log(this.getStatus(), "this.getStatus()")
            const address: any = await getChineseName(r.point.lng, r.point.lat)
            console.log(address, "address====")
            resolve({ ...address, longitude: r.point.lng, latitude: r.point.lat });
        }
        else {
            throw new Error("定位失败");
        }
    } catch (e) {
        console.log(e);
        Toast.hide();
        alert("请打开定位");
    }

}
// 获取中文地址
function getChineseName(lng, lat) {

    // @ts-ignore
    var myGeo = new BMapGL.Geocoder();
    return new Promise((resolveChangeName, rejectChangeName) => {
        // @ts-ignore
        myGeo.getLocation(new BMapGL.Point(lng, lat), function (result) {
            if (result) {
                resolveChangeName({ address: result.address })
            }
        });
    });
}
// 获取定位
export function getLocation() {
    return new Promise((resolve, reject) => {
        //@ts-ignore 开始定位 
        var geolocation = new BMapGL.Geolocation();
        geolocation.getCurrentPosition(function (r) {
            //@ts-ignore
            geolocationSuccess.call(this, r, resolve)
        });
    }).catch(e => {
        console.log(e);
    });
}

// 地址解析 通过中文获取到经纬度
export const getLocationByAddress = (address): Promise<{
    longitude: string,
    latitude: string
}> => {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        var myGeo = new BMapGL.Geocoder();
        myGeo.getPoint(address, function (point) {
            if (point) {
                console.log(
                    point, "success"
                );
                console.log(point.lng, ",", point.lat);

                resolve({ longitude: point.lng, latitude: point.lat });
            } else {
                console.log(
                    point, "您选择地址没有解析到结果"
                );

                resolve({ longitude: "", latitude: "" });
            }
        }, "北京市");
    });
}

// 判断是否为微信小程序链接
const isWxMiniProgramUrl = (url?: string) => {
    return url?.startsWith("weixin://");
};


export {
    matchSearch,
    stringifyParams,
    isIos,
    isWechat,
    is58App,
    isShenQiBao,
    pageBackFromNextPage,
    getAbsoultePath,
    getRequestParams,
    uuidv4,
    isHaojie,
    isDevelopment,
    isBuildDevelopment,
    changeParam,
    urlAddSearch,
    replaceUrl,
    getPassportUrl,
    sideTouchHandler,
    listenBack,
    resetGoBack,
    formatProtocol,
    isWxMiniProgramUrl,

}
