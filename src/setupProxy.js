/*
 * @Description:
 * @Author: du<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-11-05 14:27:47
 */
const proxy = require("http-proxy-middleware");

module.exports = function (app) {
    // 这里配置需要代理的api
    const API_ENV = process.env.REACT_APP_BUILD_TYPE;
    const localIp = "http://localhost:3001/"
    const isRd = API_ENV === "rd"

    const API_MAP = {
        // 本地mock
        dev: "http://localhost:3001/",
        // 测试环境 用不到本地代理 直接跨域请求
        QA_RELEASE: "http://localhost:3001/",
        rd: "http://*************:8001/"
    };


    const proxy_target = API_MAP[API_ENV];


    // 营销平台
    app.use(
        proxy("/bbmkt", {
            target: proxy_target,
            changeOrigin: true,
            pathRewrite: isRd ? {
                "^/bbmkt": "",

            } : {},
            headers: {
                // "actid": "97313139367953",  //兼职
                // "actid": "101514985659905",  // 团队长
                "actid": "101400447362561",  // 渠道长
                // "actid": "110956773792779" //个人
            }
        })
    );

};
