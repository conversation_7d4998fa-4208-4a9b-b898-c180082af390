import React, { useEffect, useState } from "react";
import { with<PERSON><PERSON><PERSON>, NavLink } from "react-router-dom";
import request from "api/request";
import { API } from "api/constants";
import { IMG_WOS } from "../../constants";
import "./index.scss";



const Bottom = (props) => {
    const [tabData, setTabData] = useState<any>([]);
    const obj = {
        home: {
            key: "/home",
            title: "产品推荐",
            url: `${IMG_WOS}k90ths8j__w96_h96.png`,
            greenurl: `${IMG_WOS}bhlmsauo__w96_h96.png`,
            id: 1,
            flag: true,
        },
        team: {
            key: "/team",
            title: "团队管理",
            url: `${IMG_WOS}c9p2sktf__w96_h96.png`,
            greenurl: `${IMG_WOS}tplv01ms__w96_h96.png`,
            id: 2,
            flag: false,
        },
        channel: {
            key: "/channel",
            title: "渠道管理",
            url: `${IMG_WOS}c9p2sktf__w96_h96.png`,
            greenurl: `${IMG_WOS}tplv01ms__w96_h96.png`,
            id: 3,
            flag: false,
        },
        my:
        {
            key: "/my",
            title: "业绩统计",
            url: `${IMG_WOS}uvtgbdls__w96_h96.png`,
            greenurl: `${IMG_WOS}vcurt289__w96_h96.png`,
            id: 4,
            flag: false,
        },
    }
    useEffect(() => {
        getData();
    }, []);

    const getData = () => {
        request.get(API.getSalesInfo).then((res) => {
            const { rCode, data } = res || {};
            if (rCode !== 0) return;
            const newTabData: any[] = [];
            if (data.salesType === "BIZ_STAFF" || data.salesType === "PARTTIME") {

                newTabData.push(obj.home)
                newTabData.push(obj.my)
                // setTabData((prevData: any) => [...prevData]);
                // setTabData([...tabData])

            } else if (data.salesType === "TEAM_LEADER") {

                newTabData.push(obj.home)
                newTabData.push(obj.team)
                newTabData.push(obj.my)
                // setTabData((prevData: any) => [...prevData]);
                // setTabData([...tabData])


            } else if (
                data.salesType === "SALE_CHANNEL_LEADER" &&
                data.isAuditPerson === false
            ) {

                newTabData.push(obj.home)
                newTabData.push(obj.channel)
                newTabData.push(obj.my)
                // setTabData((prevData: any) => [...prevData]);
                // setTabData([...tabData])

            } else if (
                data.salesType === "SALE_CHANNEL_LEADER" &&
                data.isAuditPerson === true
            ) {

                newTabData.push(obj.home)
                newTabData.push(obj.my)
                // setTabData((prevData: any) => [...prevData]);
                // setTabData([...tabData])

            } else if (data.salesType === null) {

                newTabData.push(obj.home)
                newTabData.push(obj.my)
                console.log(tabData, '开啊开开')
                // setTabData((prevData: any) => [...prevData]);
                // setTabData([...tabData])


            }

            setTabData([...newTabData])
        });
    };

    const point = () => {
        console.log(1)
    }
    return (
        <div className="nav-style">
            {tabData && tabData.map((item) => (
                <div className="tab-style" key={`nav${item.id}`}>
                    <NavLink to={item.key} activeClassName="active" onClick={() => point()}>
                        <div>
                            <div
                                className="tab-icon"
                                style={{
                                    background: `url(${props.location.pathname === item.key
                                        ? item.greenurl
                                        : item.url
                                        })`,
                                    backgroundSize: "cover",
                                }}
                            ></div>
                        </div>
                        <div className="tab-name">{item.title}</div>
                    </NavLink>
                </div>
            ))}
        </div>
    );
};

export default withRouter(Bottom);
