.nav-style {
    width: 100%;
    display: flex;
    justify-content: space-between;
    height: 50px;
    background-color: rgba(255, 255, 255, 1);
    padding-bottom: env(safe-area-inset-bottom);
    box-shadow: 0px 0px 5px 0px rgba(229, 229, 229, 0.5);
    .tab-style {
        flex: 1;
        text-align: center;
        a {
            display: inline-block;
            height: 100%;
            width: 100%;
            color: #000;
            touch-action: none;
            -webkit-touch-callout: none;
            user-select: none;
            -webkit-user-select: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .active {
            display: inline-block;
            display: flex;
            color: #00c682;
        }
    }

    .tab-name {
        font-size: 10px;
        margin-top: 2px;
    }
}

.tab-icon {
    // margin: 0 auto;
    width: 24px;
    height: 24px;
    background-size: cover !important;
}
