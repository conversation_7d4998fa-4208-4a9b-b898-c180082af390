import CenterPopup from "antd-mobile-v5/es/components/center-popup";
import React, { useState } from "react";
import "./index.scss";

interface IProtocolDialogProps {
    visible: boolean;
    setVisible: (visible: boolean) => void;
    protocolList: string[];
    title: string;
}
const ProtocolDialog = ({
    visible,
    setVisible,
    protocolList = [],
    title,
}: IProtocolDialogProps) => {
    const [scaleNum, setScaleNum] = useState<number>(1);
    return (
        <CenterPopup
            className="protocol-dialog"
            style={{
                "--z-index": "2000",
                "--max-width": "calc( 100vw - 52px )",
                width: "calc( 100vw - 52px )",
            }}
            visible={visible}
        >
            <div className="content">
                <div className="title">
                    {title ? title : "保险告知书"}
                    <img
                        width="13px"
                        height="13px"
                        src="//wos.58cdn.com.cn/cDazYxWcDHJ/picasso/2mtnrsbu__w51_h51.png"
                        alt="close"
                        onClick={() => {
                            setVisible(false);
                        }}
                    />
                </div>
                <div className="context">
                    <div
                        className="scale-context"
                        style={{ transform: `scale(${scaleNum})` }}
                    >
                        {protocolList.map((item) => (
                            <img key={item} src={item} width="100%" alt="" />
                        ))}
                    </div>
                </div>
                <div className="operation-iconList">
                    <img
                        src="//j1.58cdn.com.cn/jinrong/images/ems1693376990400c172dccfe109.png"
                        alt="缩小"
                        width="26px"
                        height="26px"
                        onClick={(e) => {
                            if (scaleNum <= 1) {
                                return e.preventDefault();
                            }
                            setScaleNum((num) => num - 0.1);
                        }}
                    />
                    <img
                        src="//j1.58cdn.com.cn/jinrong/images/ems169337698460158c927b17ae9.png"
                        alt="放大"
                        width="26px"
                        height="26px"
                        onClick={() => {
                            setScaleNum((num) => num + 0.1);
                        }}
                    />
                </div>
            </div>
        </CenterPopup>
    );
};

export default ProtocolDialog;
