import React, { useState, useEffect } from 'react'
import { Redirect, Route, Switch, withRouter } from "react-router-dom";

import Bottom from '../components/Bottom';
import globalStoreContext from '../globalStore'
import routers from './Router'
import { passportDistributionplatformInit } from 'api/passportUtil';

interface ICommonPageProps {
    component: React.ComponentType<any>;
    level: number | undefined;
}



// 如果后期需要添加一下公用的方法，
const CommonPage = (props: ICommonPageProps) => {
    // 判断页面是否需要进行登录
    // usePageLogin();
    const C = props.component;
    return (
        <div style={{ height: "100%", display: 'flex', flexDirection: 'column' }}>
            <C {...props} />
            {props.level === 1 && <Bottom />}
        </div>
    )
}

const MainRouter = withRouter(() => {
    return (
        <Switch>
            {
                routers.map((item, index) => {
                    return (
                        <Route key={index} path={item.path} render={props => (
                            <CommonPage level={item.level} component={item.component} {...props} />
                        )} />
                    )
                })
            }
            <Redirect to='/index' />
        </Switch>
    )
})


const App = () => {
    const [global, setGlobal] = useState({});
    useEffect(() => {
        passportDistributionplatformInit("")
    }, []);
    return (
        <globalStoreContext.Provider value={{ global, setGlobal }}>
            <MainRouter />
            <div id="passport-slide-verifycode"></div>
        </globalStoreContext.Provider>
    )
};

export default App;


