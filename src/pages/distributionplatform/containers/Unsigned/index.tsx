import React, { FC, useEffect } from "react";
import { RouteComponentProps } from "react-router-dom";
import { bridge } from '@sqb/utility';

import { IMG_WOS, CDN_WOS } from "../../constants";
import "./index.scss";

const Home: FC<{ props: RouteComponentProps<{}> }> = (props) => {
  
    useEffect(() => {
        bridge.setTitle('营销平台');
    }, [])
  

   
    return (
        <div className="unsigned">
            <img className="unsigned-img" src={`${IMG_WOS}vfmrgp1s__w960_h640.png`} alt="暂未签约"/>
            <p className="unsigned-desc">暂未签约</p>
            <div className="unsigned-qrcode">
                <img className="unsigned-qrcode-img" src={`${CDN_WOS}UvSrQpOnUza/bxscwos/%<EMAIL>`} alt="专属客服"/>
            </div>
        </div>
    );
};


export default Home;
