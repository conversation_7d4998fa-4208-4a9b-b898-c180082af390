import React, { useState, useEffect, FC, useMemo, useCallback } from 'react';
import dayjs from "dayjs";
import cx from 'classnames';
import { Popup } from 'antd-mobile-v5';
import Table from '../Components/Table';
import { LtTabs, LtTabsContent } from '../Components/LtTabs';
// 业绩总浏览
// import AchievementOverview from './AchievementOverview';
import BaseInfo from '../Components/BaseInfo';
import NDCalenderPicker from '../Components/NDCalenderPicker';
import RadioPopup from '../Components/RadioPopup';
import FilterPopup from '../Components/FilterPopup';
import LoadMore from '../Components/LoadMore';
import AdditionalRemarksPopup from '../Components/AdditionalRemarksPopup';
import PerformanceOverview from './PerformanceOverview';
import {
    daikuanOverViewColumn,
    personalStatusoptions,
    loanTeamMembersColumns,
    loanTabs,
    PerformanceOverviewColumn
} from '../constants';
import { getloanPersonalColumns, getTeamColumns } from './tableColumns';
import { getPerformanceOverview, getPersonInfo, getTeamOrMemberPerformance, getTeamMemberPerformance, getSelfPerformance } from '../common';
import type {
    IUserType, ITeamOrTeamMember, ISelfInfo,
    ITeamOrTeamMemberParams, ISelfInfoParams,
    IPerformanceInfo
} from '../interface';

import './index.scss';

interface Iprops {

}
const StartTimeInit = dayjs().startOf('day').format("YYYY-MM-DD HH:mm:ss");
const EndTimeInit = dayjs().endOf('day').format("YYYY-MM-DD HH:mm:ss");
const TimeLabelInit = '本日';
const initialPageConfig = {
    "pageNo": 1,
    "pageSize": 10,
}

const LoanType: FC<Iprops> = ({ }) => {
    // 团队业绩 团队成员业绩 本人业绩 tabs key
    const [activeKey, setActiveKey] = useState("");

    // 状态弹窗
    const [statusRadioVisible, setStatusRadioVisible] = useState(false);
    // 时间弹窗
    const [timePopupVisible, setTimePopupVisible] = useState(false);
    // 其他条件查询弹窗
    const [filterVisible, setFilterVisible] = useState<boolean>(false);
    // 查询条件的formdata
    const [filterFormData, setFilterFormData] = useState({
        idCodeOrApplicantName: "",
        isOverdue: "",
        isOverdueLabel: "",
        statusValue: personalStatusoptions[1].value,
        statusLabel: personalStatusoptions[1].label,
        // 个人业绩日历筛选数据
        personTimeValue: null,
        personTimeLabel: TimeLabelInit,
        personTimeKey: "",
        // 团队业绩日历筛选数据
        teamTimeValue: null,
        teamTimeLabel: TimeLabelInit,
        teamTimeKey: "",
    });
    // 业绩总览数据
    const [overViewData, setOverViewData] = useState({
        "todayApplicantCount": "",
        "weekApplicantCount": "",
        "monthApplicantCount": "",
        "todayApprovedApplicantCount": "",
        "weekApprovedApplicantCount": "",
        "monthApprovedApplicantCount": "",
        // "todayCommission": "",
        // "weekCommission": "",
        // "monthCommission": "",
        // "totalCommission": ""
    });
    // salesType 登录人身份 BIZ_STAFF业务员 TEAM_LEADER团队长 SALE_CHANNEL_LEADER渠道负责人
    const [salesType, setSalesType] = useState<IUserType>()
    // 团队业绩数据
    const [teamData, setTeamData] = useState<ITeamOrTeamMember[]>([]);
    // 团队业绩/团队成员数据 请求参数
    const [teamParams, setTeamParams] = useState<ITeamOrTeamMemberParams>({
        "sortField": "",
        "sortBy": "",
        "startTime": StartTimeInit,
        "endTime": EndTimeInit,
        ...initialPageConfig
    });
    // 团队业绩/团队成员数据 总条数
    const [teamTotal, setTeamTotal] = useState(0);

    // 团队成员数据
    const [teamMemberData, setTeamMemberData] = useState<ITeamOrTeamMember[]>([]);
    // 本人业绩数据
    const [personData, setPersonData] = useState<ISelfInfo[]>([]);
    // 本人业绩 请求参数
    const [personParams, setPersonParams] = useState<ISelfInfoParams>({
        "auditStatus": 0,
        "pageNo": 1,
        "pageSize": 10,
        "startTime": StartTimeInit,
        "endTime": EndTimeInit,
    })
    // 本人业绩 总条数
    const [personTotal, setPersonTotal] = useState(0);

    // 业绩总计数据,key值对应tab的key
    // 1:团队业绩 2:团队成员业绩 3:本人业绩
    const [performanceData, setPerformanceData] = useState<{ [props: string]: IPerformanceInfo }>({
        "1": {},
        "2": {},
        "3": {},
    });

    // 补充说明弹窗
    const [additionalRemarksVisible, setAdditionalRemarksVisible] = useState<boolean>(false);
    // 补充说明弹窗 id(mock:1866384644453052417)
    const [additionalRemarksId, setAdditionalRemarksId] = useState<string>("");

    // Add new state for team member popup
    const [teamMemberPopupVisible, setTeamMemberPopupVisible] = useState(false);
    const [selectedTeamMemberData, setSelectedTeamMemberData] = useState<ITeamOrTeamMember[]>([]);

    useEffect(() => {
        getInit();
    }, []);

    useEffect(() => {
        if (activeKey) {
            // 如果数据已经存在 切换tab时 不再请求
            if ((activeKey === "1" && teamData.length === 0)
                || (activeKey === "2" && teamMemberData.length === 0)
            ) {
                getTeamOrTeamMemberTableData(teamParams)
                return;
            }

            if (personData.length === 0 && activeKey === "3") {
                getPersonTableData(personParams);
                return;
            }

        }
    }, [activeKey])

    const getInit = () => {
        getPerformanceOverview().then(res => {
            if (res) {
                setOverViewData(res)
            }
        })
        getPersonInfo().then(res => {
            if (res) {
                setSalesType(res.salesType)
            }
        })
    }

    // 元换算万元
    function formatMoney(value: number = 0) {
        return Number((value / 10000).toFixed(2));
    }

    // 处理setPerformanceData(res);
    const handlePerformanceData = (res) => {
        setPerformanceData({
            ...performanceData,
            [activeKey]: {
                "totalApplicantCount": res.totalApplicantCount || 0,
                "totalApprovedApplicantCount": res.totalApprovedApplicantCount || 0,
                "totalLineOfCredit": formatMoney(res.totalLineOfCredit),
                "totalLoanBalance": formatMoney(res.totalLoanBalance),
            },
        });
    }

    // 获取团队或者团队成员table信息 初始化放在useEffect里面 后续切换tab的时候调用
    const getTeamOrTeamMemberTableData = useCallback((params: ITeamOrTeamMemberParams) => {
        getTeamOrMemberPerformance(params).then(res => {
            if (res) {
                if (activeKey === "1") {
                    // 团队业绩
                    if (params.pageNo > teamParams.pageNo) {
                        setTeamData([...teamData, ...res.list]);
                    } else {
                        setTeamData(res.list);
                    }
                } else if (activeKey === "2") {
                    // 团队成员
                    if (params.pageNo > teamParams.pageNo) {
                        setTeamMemberData([...teamMemberData, ...res.list]);

                    } else {
                        setTeamMemberData(res.list);
                    }
                }
                // 团队业绩和团队成员不会同时出现 数据还类似 直接用同一个字段俩统计
                setTeamParams(params);
                setTeamTotal(res.total);
                handlePerformanceData(res);
            }
        })
    }, [activeKey, teamParams, teamData, teamMemberData])

    // 获取本人table信息
    const getPersonTableData = useCallback((params: ISelfInfoParams) => {

        const newParams = {
            ...params,
            auditStatus: params.auditStatus ?? filterFormData.statusValue
        }
        console.log(newParams, '------person');
        getSelfPerformance(newParams).then(res => {
            if (res) {
                // mock
                // if (res.list && res.list[0]) res.list[0].isSupplement = 1;

                if (newParams.pageNo > personParams.pageNo) {
                    setPersonData([...personData, ...res.list]);
                } else {
                    setPersonData(res.list);
                }
                setPersonTotal(res.total);
                setPersonParams(newParams);
                handlePerformanceData(res);
            }
        })
    }, [activeKey, filterFormData.statusValue, personParams, personData])

    // 加载更多
    const loadMore = useCallback(() => {
        console.log("loadMore");
        if (activeKey === "3") {
            const nextParams = {
                ...personParams,
                pageNo: personParams.pageNo + 1
            }
            getPersonTableData(nextParams)
        } else {
            const nextParams = {
                ...teamParams,
                pageNo: teamParams.pageNo + 1
            }

            getTeamOrTeamMemberTableData(nextParams)
        }
    }, [getPersonTableData, activeKey, teamParams, getTeamOrTeamMemberTableData])

    // 团队业绩 本人业绩 切换
    const onChange = useCallback((key) => {
        setActiveKey(key);
    }, [setActiveKey])

    // 状态弹窗 选中的值 改变
    const onStatusChange = useCallback((valueObj) => {
        console.log(valueObj);
        setFilterFormData({
            ...filterFormData,
            statusValue: valueObj.value,
            statusLabel: valueObj.label,
        });
        // 关闭弹窗
        setStatusRadioVisible(false);

        const newParams = {
            ...personParams,
            ...initialPageConfig,
            auditStatus: valueObj.value

        }
        getPersonTableData(newParams)
    }, [personParams, getPersonTableData])

    // 其他条件弹窗 选中的值 改变
    // 筛选条件点击确定 状态设置为全部 时间设置为一年
    const onFilterChange = useCallback((valueObj) => {
        console.log(valueObj, '------filter');
        // personTimeValue: null;
        // personTimeLabel: string;
        // personTimeKey: string;
        // teamTimeValue: null;
        // teamTimeLabel: string;
        // teamTimeKey: string;
        let timeObj: any = {};
        let timeParams: any = {};
        // TODO:
        console.log(dayjs().subtract(1, 'year').toDate());

        if (activeKey === "3") {
            // 本人
            timeObj = {
                personTimeValue: [dayjs().subtract(1, 'year'), dayjs()],
                personTimeLabel: "本年",
                personTimeKey: "4",
            }
            timeParams = {
                startTime: timeObj.personTimeValue[0].format("YYYY-MM-DD HH:mm:ss"),
                endTime: timeObj.personTimeValue[1].format("YYYY-MM-DD HH:mm:ss"),
            }

        } else {
            // 团队
        }
        setFilterFormData({
            ...filterFormData,
            idCodeOrApplicantName: valueObj.name,
            isOverdue: valueObj.overdue,
            isOverdueLabel: valueObj.overdueLabel,
            statusValue: personalStatusoptions[0].value,
            statusLabel: personalStatusoptions[0].label,
            ...timeObj
        });
        // 关闭弹窗
        setFilterVisible(false);

        const newParams = {
            ...personParams,
            ...initialPageConfig,
            idCodeOrApplicantName: valueObj.name,
            isOverdue: valueObj.overdue,
            auditStatus: personalStatusoptions[0].value,
            ...timeParams

        }
        getPersonTableData(newParams)
    }, [personParams, getPersonTableData])

    const handleShowTimePopup = () => {
        setTimePopupVisible(true);
    }

    // 处理时间展示 跨年和非当年的展示年份，本年度的不展示年份
    const handleTimeLabel = (date) => {
        let formatValue = dayjs(date[0]).get('year') !== dayjs().get('year')
            ? "YY/MM/DD"
            : "MM/DD"
            ;

        const startTimeLabel = dayjs(date[0]).format(formatValue);
        const endTimeLabel = dayjs(date[1]).format(formatValue);

        let t = `${startTimeLabel}-${endTimeLabel}`;
        return t;
    }


    // 日期范围选择器确定 回调
    const onCalendarChange = (values) => {
        const { date, weekDayMonth, weekDayMonthKey } = values;
        const timeLabelTemp = weekDayMonth || handleTimeLabel(date);
        const startTime = dayjs(date[0]).startOf('day').format("YYYY-MM-DD HH:mm:ss");
        const endTime = dayjs(date[1]).endOf('day').format("YYYY-MM-DD HH:mm:ss");

        if (activeKey === "3") {
            setFilterFormData({
                ...filterFormData,
                personTimeValue: date,
                personTimeLabel: timeLabelTemp,
                personTimeKey: weekDayMonthKey,
            });

            const newParams = {
                ...personParams,
                ...initialPageConfig,
                startTime,
                endTime,
            }
            getPersonTableData(newParams)
        } else {
            setFilterFormData({
                ...filterFormData,
                teamTimeValue: date,
                teamTimeLabel: timeLabelTemp,
                teamTimeKey: weekDayMonthKey,
            });
            const newParams = {
                ...teamParams,
                ...initialPageConfig,
                startTime,
                endTime,
            }
            getTeamOrTeamMemberTableData(newParams)
        }

    }
    const onSort = useCallback((dataIndex, sortBy) => {
        // 本人目前没添加排序功能
        const newParams = {
            ...teamParams,
            ...initialPageConfig,
            sortField: dataIndex,
            sortBy
        }
        getTeamOrTeamMemberTableData(newParams);

    }, [teamParams, getTeamOrTeamMemberTableData])
    // 状态点击
    const onStatusClick = () => {
        setStatusRadioVisible(true);
    }
    // 其他条件查询点击
    const onFilterClick = () => {
        setFilterVisible(true);
    }
    // 打开补充说明弹窗
    const handleShowAdditionalRemarksPopup = (id) => {
        setAdditionalRemarksVisible(true);
        setAdditionalRemarksId(id);
    }

    // 打开团队成员明细
    const handleShowTeamMemberDetailPopup = useCallback((teamRow) => {
        const newParams = {
                ...teamParams,
                teamId: teamRow.teamId, 
                productId: teamRow.productId 
            }
        getTeamMemberPerformance(newParams).then(res => {
            if (res) {
                setSelectedTeamMemberData(res.list);
            }
        });
        setTeamMemberPopupVisible(true);
    }, [teamParams, teamData])
    
    const personColumns = useMemo(() => {
        return getloanPersonalColumns(filterFormData.statusValue, handleShowAdditionalRemarksPopup);
    }, [filterFormData.statusValue])

    const teamColumns = useMemo(() => {
        return getTeamColumns(handleShowTeamMemberDetailPopup);
    }, [teamParams])

    // 根据角色渲染tab
    const renderTab = useMemo(() => {
        let filterTabs = loanTabs;
        if (salesType === "BIZ_STAFF" || salesType === "PARTTIME") {
            filterTabs = loanTabs.slice(2);
            // 普通业务员只展示本人业绩
        } else if (salesType === "SALE_CHANNEL_LEADER") {
            // 渠道长展示团队业绩和本人业绩
            filterTabs = loanTabs.filter(item => item.key !== "2");
        } else if (salesType === "TEAM_LEADER") {
            // 团队长只展示团队成员业绩和本人业绩
            filterTabs = loanTabs.filter(item => item.key !== "1");
        }
        if (salesType) {
            setActiveKey(filterTabs[0].key);
        }
        return filterTabs;

    }, [salesType]);

    // 渲染其他条件查询内容
    const renderFilterContent = useMemo(() => {
        const arr: string[] = [];
        const { idCodeOrApplicantName, isOverdueLabel } = filterFormData;
        if (idCodeOrApplicantName) {
            arr.push(idCodeOrApplicantName);
        }
        if (isOverdueLabel) {
            arr.push(isOverdueLabel);
        }
        return (
            <span>
                {arr.join('，')}
            </span>
        )
    }, [filterFormData.idCodeOrApplicantName, filterFormData.isOverdueLabel])

    const handleCloseAdditionalRemarksPopup = () => {
        setAdditionalRemarksVisible(false);
        setAdditionalRemarksId('');
    }

    const handleSubmitAdditionalRemarks = (id) => {
        const personDataTemp = personData.map((item) => {
            if (item.id === id && item.isSupplement) {
                return {
                    ...item,
                    isSupplement: 0,
                }
            }
            return item;
        });
        setPersonData(personDataTemp);

        handleCloseAdditionalRemarksPopup();
    }

    return (
        <div className='dis_loan_type'>
            {/* 业绩总览 */}
            {/* <AchievementOverview total={overViewData.totalCommission} /> */}
            {/* 详细信息 */}
            <BaseInfo columns={daikuanOverViewColumn} dataSource={overViewData} />
            <div className="dis_loan_type_tab_fix">
                <LtTabs tabs={renderTab} onChange={onChange} activeKey={activeKey} />

                <div
                    className={cx('dis_filter_item dis_filter_time', {
                        'dis_filter_has_value': filterFormData.personTimeLabel || filterFormData.teamTimeLabel
                    })}
                    onClick={handleShowTimePopup}
                >
                    {activeKey === '3'
                        ? filterFormData.personTimeLabel
                        : filterFormData.teamTimeLabel
                    }
                </div>
                <NDCalenderPicker
                    dateValue={
                        activeKey === '3' ? filterFormData.personTimeValue : filterFormData.teamTimeValue
                    }
                    dateKey={
                        activeKey === '3' ? filterFormData.personTimeKey : filterFormData.teamTimeKey
                    }
                    visible={timePopupVisible}
                    setVisible={setTimePopupVisible}
                    onConfirm={onCalendarChange}
                />
            </div>

            {/*  条件筛选部分，只个人业绩 展示 */}
            {activeKey === loanTabs[2].key && (
                <div className="dis_condition_box">
                    <ul className="dis_condition_content">
                        <li onClick={onStatusClick}>
                            <span className={cx('dis_filter_item dis_condition_status', {
                                'dis_filter_has_value': typeof filterFormData.statusValue === 'number'
                            })}>
                                {filterFormData.statusLabel}
                            </span>
                        </li>
                        <li onClick={onFilterClick}>
                            {(filterFormData.idCodeOrApplicantName !== '' || filterFormData.isOverdueLabel !== '')
                                ? (
                                    <span className="dis_filter_item dis_filter_no_arrow dis_filter_has_value">
                                        {renderFilterContent}
                                    </span>
                                )
                                : (
                                    <span className="dis_filter_item dis_filter_no_arrow">
                                        其他条件查询
                                    </span>
                                )
                            }
                        </li>
                    </ul>
                </div>
            )}

            {/* 业绩总计 */}
            <PerformanceOverview
                columns={PerformanceOverviewColumn}
                dataSource={performanceData[activeKey]}
            />

            {/* 团队表格 */}
            <LtTabsContent activeKey={activeKey} lkey={loanTabs[0].key}>
                <Table onSort={onSort} columns={teamColumns} dataSource={teamData} />
                {
                    teamData.length === 0
                        ? null
                        : <LoadMore hasMore={teamTotal > teamData.length} onClick={loadMore} />
                }

            </LtTabsContent>
            {/* 团队成员 */}
            <LtTabsContent activeKey={activeKey} lkey={loanTabs[1].key}>
                <Table onSort={onSort} columns={loanTeamMembersColumns} dataSource={teamMemberData} />
                {
                    teamMemberData.length === 0
                        ? null
                        : <LoadMore hasMore={teamTotal > teamMemberData.length} onClick={loadMore} />
                }
            </LtTabsContent>
            {/* 个人业绩 */}
            <LtTabsContent activeKey={activeKey} lkey={loanTabs[2].key}>
                <Table
                    columns={personColumns}
                    dataSource={personData}
                />
                {
                    personData.length === 0
                        ? null
                        : <LoadMore hasMore={personTotal > personData.length} onClick={loadMore} />
                }

            </LtTabsContent>
            {/* 状态选择弹框 */}
            <RadioPopup
                options={personalStatusoptions}
                value={filterFormData.statusValue}
                onChange={onStatusChange}
                visible={statusRadioVisible}
                setVisible={setStatusRadioVisible}
            ></RadioPopup>

            {/* 其他条件查询弹框 */}
            <FilterPopup
                onChange={onFilterChange}
                visible={filterVisible}
                setVisible={setFilterVisible}
            />

            {/* 补充说明弹窗 */}
            <AdditionalRemarksPopup
                id={additionalRemarksId}
                visible={additionalRemarksVisible}
                onCloseFn={handleCloseAdditionalRemarksPopup}
                onSubmitCb={handleSubmitAdditionalRemarks}
            ></AdditionalRemarksPopup>

            {/* 团队成员明细，弹窗显示 */}
            <Popup
                visible={teamMemberPopupVisible}
                onClose={() => setTeamMemberPopupVisible(false)}
                position='bottom'
                bodyStyle={{ height: '80vh' }}
                className="team-member-popup"
            >
                <div className="popup-header">
                    <div className='title'>团队成员详情</div>
                    <div
                        className="close-btn"
                        onClick={() => setTeamMemberPopupVisible(false)}
                    />
                </div>
                <div className="popup-content">
                    <Table
                        columns={loanTeamMembersColumns}
                        dataSource={selectedTeamMemberData}
                    />
                </div>
            </Popup>
        </div>
    )
}

export default LoanType;
