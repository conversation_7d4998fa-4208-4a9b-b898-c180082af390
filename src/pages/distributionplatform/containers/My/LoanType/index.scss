@import "~scss/mixins/px2rem";
@import "~scss/index";

.dis_loan_type {
    // bttom btn height
    padding-bottom: rem(100);
    &_title {
        font-size: rem(36);
        padding-left: rem(24);
        margin-bottom: rem(24);
    }

    .dis_loan_type_tab_fix {
        background-color: white;
        position: sticky;
        top: 0;
        z-index: 20;
    }
    .dis_filter_item {
        font-family: PingFangSC-Regular;
        font-size: rem(28);
        font-weight: 400;
        line-height: rem(40);
        color: #333;
        display: flex;
        align-items: center;

        &::after {
            content: '';
            display: block;
            width: rem(28);
            height: rem(28);
            background: url(#{$wos-cdn-1}s7a8lmai__w56_h56.png) no-repeat center;
            background-size: contain;
        }
    }

    .dis_filter_time, .dis_my_calender_box {
        color: #333 !important;
        position: absolute;
        top: rem(21);
        right: rem(24);
    }

    .dis_base_infomation {
        margin-bottom: rem(36);
    }
    .dis_day_week_month_box {
        margin-left: rem(24);
        display: flex;
        align-items: start;
    }
    .dis_condition_box {
        display: flex;
        justify-content: space-between;
        margin-top: rem(38);
        margin-bottom: rem(24);
        padding: 0 rem(24);
    }
    .dis_condition_content {
        display: flex;
        flex-wrap: wrap;

        li {
            padding: rem(12) rem(16);
            background-color: #F5F5F5;
            border-radius: rem(16);
            font-family: PingFangSC-Regular;
            font-size: rem(28);
            font-weight: 400;
            line-height: rem(40);
            color: #666;
            margin-right: rem(16);
        }
    }
    .dis_filter_has_value {
        font-family: PingFangSC-Semibold;
        font-weight: 700;
        color: #00c682;
    }

    .dis_filter_no_arrow {
        &::after {
            display: none;
        }
    }

    .dis_condition_status {
        &::after {
            background-image: url(#{$wos-cdn-1}m13v8vs3__w56_h56.png);
        }
    }
    
    .dis_current_status {
        color: #00c682;
        display: flex;
        align-items: center;
        font-size: rem(28);
        font-family: PingFangSC-Semibold;
        margin-right: rem(24);
        img {
            width: rem(28);
        }
    }
}
.team-member-popup {
    .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: rem(0);
        border-bottom: 1px solid #e5e5e5;

        .title {
            font-size: rem(40);
            font-weight: 500;
            color: #333;
        }

        .close-btn {
            width: rem(56);
            height: rem(56);
            background: url("#{$wos-cdn-1}upcah6pr__w56_h56.png") no-repeat center;
            background-size: cover;
            cursor: pointer;
        }
    }

    .popup-content {
        padding: rem(0);
    }
}