
import React, { memo, FC } from 'react';
import type { IPerformanceColumnItem, IPerformanceInfo } from '../../interface';
import './index.scss';

interface IProps {
    columns: IPerformanceColumnItem[];
    dataSource: IPerformanceInfo;
}

const PerformanceOverview: FC<IProps> = ({ columns = [], dataSource = {}}) => {
    return (
        <div className="dis_performance_overview">
            <div className="dis_performance_overview_content">
                <ul>
                    {columns.map((item, i) => (
                        <li className='dis_performance_item' key={`performance${i}`}>
                            <p className='dis_performance_num'>
                                <strong>{dataSource[item.dataKey] || 0}</strong>
                                {item.unit}
                            </p>
                            <span>{item.name}</span>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    )
}

export default memo(PerformanceOverview);