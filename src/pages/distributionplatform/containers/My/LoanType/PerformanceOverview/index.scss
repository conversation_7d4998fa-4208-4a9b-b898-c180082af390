@import "~scss/mixins/px2rem";

.dis_performance_overview {
    margin: rem(24);

    .dis_performance_overview_content {
        height: rem(126);
        background: linear-gradient(180deg, rgba(255, 238, 220, 0.26) 0%,rgba(255, 255, 255, 0.1) 100%);
        border: 1px solid rgba(249, 190, 122, 0.2);
        border-radius: rem(16);
        position: relative;

        &::after {
            content: '';
            display: block;
            height: 70%;
            position: absolute;
            bottom: -2px;
            left: -2px;
            right: -2px;
            background: linear-gradient(180deg, rgba(255,255,255,0), #fff 90%);
        }

        ul {
            display: flex;
            justify-content: space-around;
            width: 100%;
            position: absolute;
            top: rem(24);
            left: 0;
            right: 0;
            z-index: 1;
        }
    }

    .dis_performance_item {
        width: 25%;
        font-family: PingFangSC-Regular;
        font-size: rem(24);
        font-weight: 400;
        line-height: rem(33);
        color: #666;
        text-align: center;
    }

    .dis_performance_num {
        font-size: rem(20);
        line-height: 1;
        color: #333;
        margin-bottom: rem(12);

        strong {
            font-family: DINAlternate-Bold;
            font-size: rem(32);
            font-weight: 700;
            line-height: rem(37);
            margin-right: rem(4);
        }
    }
}