/*
 * @Author: z<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-30 16:27:55
 * @LastEditors: zhangwu01 <EMAIL>
 * @LastEditTime: 2024-11-18 10:44:05
 * @Description: 贷款 业绩纵览部分
 * 
 */
import React, { memo } from 'react';
import './index.scss';

const AchievementOverview = ({ total = "" }) => {
    return (
        <div>
            <h4 className='dis_loan_type_title'>业绩总览</h4>
            <div className="dis_loan_type_total_amount_box">
                <img src="//wos.58cdn.com.cn/cDazYxWcDHJ/picasso/ssrg8b3i__w168_h168.png" />
                <div className="dis_loan_type_total_amount_box_money">
                    <p>
                        {total}
                        <span className='dis_loan_type_total_amount_box_money_unit'>元</span>
                    </p>
                    <p className="dis_loan_type_total_amount_box_money_desc">
                        营销总佣金
                    </p>
                </div>
            </div>
        </div>
    )
}

export default memo(AchievementOverview);