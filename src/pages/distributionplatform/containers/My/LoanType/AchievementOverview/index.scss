@import "~scss/mixins/px2rem";

.dis_loan_type_total_amount_box {
    display: flex;
    padding: rem(28) 0 rem(28) rem(32);
    box-sizing: border-box;
    background: linear-gradient(90deg, rgba(255, 88, 0, 0.04) 0%, rgba(255, 88, 0, 0) 100%);
    margin-bottom: rem(24);
    img {
        width: rem(84);
        height: rem(84);
        margin-right: rem(24);
    }
    &_money {
        font-family: DINAlternate-Bold;
        font-size: rem(52);
        &_unit {
            font-size: rem(24);
            margin-left: rem(4);
            font-family: PingFangSC-Semibold;
        }
        &_desc {
            margin-top: rem(8);
            font-family: PingFangSC-Regular;
            font-size: rem(24);
            color: #666666;
        }
    }
}
