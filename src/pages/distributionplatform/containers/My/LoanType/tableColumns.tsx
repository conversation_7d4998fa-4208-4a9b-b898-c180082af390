
import React from "react";

import { personalStatusEnumMap } from "../constants";
// import { Link } from "react-router-dom";

// 贷款-个人业绩 table column
// 个人业绩需要根据不同 状态展示不同内容
export const getloanPersonalColumns = (status, uploadClick) => {
    const loanPersonalColumns = [
        {
            title: "姓名",
            dataIndex: "applicantName",
            fixed: "left",
            key: "applicantName"
        },
        {
            title: "产品",
            dataIndex: "productName",
            key: "productName"
        },
        {
            title: "申请时间",
            dataIndex: "applyTime",
            key: "applyTime"
        },
        // 审批时间 审核通过 已失效 审核失败展示
        {
            title: "审批时间",
            dataIndex: "auditTime",
            key: "auditTime",
        },
        {
            title: "审批状态",
            dataIndex: "auditStatus",
            key: "auditStatus",
            hidden: status !== personalStatusEnumMap.all

        },
        {
            title: "逾期天数",
            dataIndex: "overdueDays",
            key: "overdueDays",
            hidden: status !== personalStatusEnumMap.all

        },
        {
            title: "失败原因",
            dataIndex: "auditRemark",
            key: "auditRemark",
            hidden: status !== personalStatusEnumMap.all
        },

        {
            title: "身份证号",
            dataIndex: "applicantIdCode",
            key: "applicantIdCode"
        },
        {
            title: "手机号",
            dataIndex: "applicantMobile",
            key: "applicantMobile"
        },

        // 审核通过 已失效 展示 授信额度（元） 授信日利率 授信期限（月） 还款方式 贷款余额（元）
        {
            title: "授信额度（万元）",
            dataIndex: "lineOfCredit",
            key: "lineOfCredit",
            render: (text) => {
                return text && (text / 10000).toFixed(2);
            },
            hidden: status === personalStatusEnumMap.auditting || status === personalStatusEnumMap.failed
        },
        {
            title: "授信日利率（‱）",
            dataIndex: "creditRate",
            key: "creditRate",
            hidden: status === personalStatusEnumMap.auditting || status === personalStatusEnumMap.failed

        },
        {
            title: "授信期限（月）",
            dataIndex: "creditTerm",
            key: "creditTerm",
            hidden: status === personalStatusEnumMap.auditting || status === personalStatusEnumMap.failed

        },
        {
            title: "还款方式",
            dataIndex: "repaymentType",
            key: "repaymentType",
            hidden: status === personalStatusEnumMap.auditting || status === personalStatusEnumMap.failed

        },
        {
            title: "贷款余额(元)",
            dataIndex: "loanBalance",
            key: "loanBalance",
            hidden: status === personalStatusEnumMap.auditting || status === personalStatusEnumMap.failed
        },
        // 审核失败展示：失败原因
        {
            title: "失败原因",
            dataIndex: "auditRemark",
            key: "auditRemark",
            hidden: status !== personalStatusEnumMap.failed
        },
        {
            title: "操作",
            dataIndex: "btn",
            key: "btn",
            render: (text, row) => {
                return <span
                    style={{ color: "#00C682" }}
                    onClick={() => uploadClick(row.id)}
                >
                    上传资料
                </span>
                // return <Link style={{ color: "#00C682" }} to={`/AdditionalRemarks/${row.id}`}>上传资料 </Link>
            }
        }
    ];
    const realRenderColumns = loanPersonalColumns.filter(item => {
        return !item.hidden
    });
    return realRenderColumns;
}


// 贷款-团队业绩 table column
export const getTeamColumns = (detailClick) => {
    const loanTeamColumns = [
    {
        title: "团队名称",
        dataIndex: "teamName",
        key: "teamName",
        fixed: "left",
        sortable: true,
        render: (text, row) => {
            return row.teamId !== row.teamLeaderId ? (<span
                style={{ color: "#00C682" }}
                onClick={() => detailClick(row)}
            >
                {text}
            </span>) : (<span>{text}</span>)
        }
    },
    {
        title: "团队长",
        key: "teamLeaderName",
        sortable: true,
        dataIndex: "teamLeaderName",
    },
    {
        title: "产品",
        sortable: true,
        dataIndex: "productName",
        key: "productName",
    },
    {
        title: "申请客户数",
        sortable: true,
        dataIndex: "applicantCount",
        key: "applicantCount",
    },
    {
        sortable: true,
        title: "审批通过客户数",
        dataIndex: "approvedApplicantCount",
        key: "approvedApplicantCount",
    },
    {
        sortable: true,
        title: "授信额度(万元)",
        dataIndex: "lineOfCredit",
        key: "lineOfCredit",
        render: (text) => {
            return text && (text / 10000).toFixed(2);
        },
    },
    {
        sortable: true,
        title: "贷款余额(万元)",
        dataIndex: "loanBalance",
        key: "loanBalance",
        render: (text) => {
            return text && (text / 10000).toFixed(2);
        },
    },
    ]
    return loanTeamColumns;
}