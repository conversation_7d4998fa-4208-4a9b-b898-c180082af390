@import "~scss/mixins/px2rem";

.dis_loan_type_btn_box {
    // position: fixed;
    // bottom: 0;
    background-color: white;
    width: 100%;
    display: flex;
    justify-content: center;
    z-index: 10;
    bottom: 47px;
    padding: rem(16) 0;
    .adm-button {
        --background-color: #00c682;
        font-family: PingFangSC-Medium;
        --text-color: #fff;
        font-size: rem(32);
        padding: rem(23) 0;
        text-align: center;
        width: rem(331);
        border-radius: rem(72);
    }
    .adm-button:first-child {
        margin-right: rem(24);
    }
}
