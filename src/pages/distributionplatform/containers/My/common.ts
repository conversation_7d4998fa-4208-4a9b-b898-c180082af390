
import request from "src/api/request";
import { Toast } from "antd-mobile-v5";
import { API } from "api/constants";
import type { ILoanOverView, ISalesInfo, ITeamOrTeamMember, ISelfInfo } from "./interface";



type IMethodsType = "get" | "post" | "jsonp";
const commonRequestHandle = async (url = "", type: IMethodsType = "get", params: ICO = {}, headers: ICO = {}, otherOptions: ICO = {}) => {
    const { isEncrypted, isDecrypted } = otherOptions;
    try {
        const res = await request[type](url, params, headers, isEncrypted, isDecrypted);
        const { rMsg = "", rCode, data } = res;
        if (`${rCode}` !== "0") {
            if (rMsg) {
                Toast.show(rMsg || "系统错误");
            }
            return false;
        }
        return data || true
    } catch (error) {
        Toast.show("系统错误");
        return false;

    }
}

// 贷款-总览
export const getPerformanceOverview = (): Promise<ILoanOverView> => {
    return commonRequestHandle(API.getPerformanceOverview, "get", {})
}
// 团队业绩/成员业绩
export const getTeamOrMemberPerformance = (params = {}): Promise<{
    list: ITeamOrTeamMember[],
    total: number,
    totalApplicantCount: number,
    totalApprovedApplicantCount: number,
    totalLineOfCredit: number,
    totalLoanBalance: number,
}> => {
    return commonRequestHandle(API.getTeamPerformance, "post", params)
}
// 团队业绩详情
export const getTeamMemberPerformance = (params = {}): Promise<{
    list: ITeamOrTeamMember[],
    total: number,
    totalApplicantCount: number,
    totalApprovedApplicantCount: number,
    totalLineOfCredit: number,
    totalLoanBalance: number,
}> => {
    return commonRequestHandle(API.getTeamMemberPerformance, "post", params)
}
// 个人业绩
export const getSelfPerformance = (params = {}): Promise<{
    list: ISelfInfo[],
    total: number,
    totalApplicantCount: number,
    totalApprovedApplicantCount: number,
    totalLineOfCredit: number,
    totalLoanBalance: number,
}> => {
    return commonRequestHandle(API.getMyselfPerformance, "post", params)
}
// 获取当前登录人身份
export const getPersonInfo = (): Promise<ISalesInfo> => {
    return commonRequestHandle(API.getSalesInfo, "get", {})
}