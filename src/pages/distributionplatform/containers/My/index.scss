@import "~scss/index";
@import "~scss/mixins/px2rem";

.performance {
    height: 100%;
    overflow-y: auto;
    background: #fff;
    // margin-bottom: 52px;
    .performance-total {
        height: 38px;
        background: linear-gradient(90deg, rgba(255, 88, 0, 0.04) 0%, rgba(255, 88, 0, 0) 100%);
        border-radius: 8px;
        margin: 16px 8px 12px;
        line-height: 38px;
        padding-left: 12px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
        .total-num {
            font-weight: 700;
            color: #000;
        }
    }
    .performance-all {
        height: 100%;
        background: url(#{$wos-cdn-1}cfun77o9__w1500_h1052.png) no-repeat;
        background-position: top center;
        background-size: contain;
    }
    .performance-all-div {
        display: flex;
    }
    .performance-all-overview {
        display: flex;
        font-family: PingFangSC-Semibold;
        font-size: 18px;
        font-weight: 700;
        color: rgba(19, 20, 21, 1);
        margin-left: 12px;
        justify-content: center;
        align-items: center;
    }

    .platformhome-title {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .platformhome-title-num {
            font-size: 26px;
            font-weight: 700;
            color: rgba(51, 51, 51, 1);
        }
        .platformhome-title-i {
            display: flex;
        }
        .platformhome-title-image-i {
            width: 12.5px;
            height: 12.5px;
            margin-left: 6px;
            margin-top: 4px;
        }
        .platformhome-title-mon {
            font-family: PingFangSC-Regular;
            font-size: 12px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
            margin-top: 3px;
        }
    }
    .performance-top-content {
        display: flex;
        justify-content: space-between;

        .performance-top-content-right {
            display: flex;
            margin-top: 32px;
            margin-right: 12px;
            width: 78px;
            height: 30px;
            color: #00c682;
            font-size: 14px;
            line-height: 30px;
            font-family: PingFangSC-Semibold;
            text-align: center;
            justify-content: center;
            border: 1px solid #00c682;
            border-radius: 15px;
            .performance-top-content-button {
                margin-left: 14px;
                width: 78px;
                height: 30px;
            }
            .performance-top-content-image {
                width: 14px;
                height: 14px;
                padding-right: 14px;
                justify-content: center;
                display: block;
                margin: auto;
            }
        }
    }
    .performance-top {
        height: 100px;
        display: flex;
        align-items: center;

        .performance-top-img {
            width: 44px;
            height: 44px;
        }

        .performance-top-img img {
            width: 44px;
            height: 44px;
        }

        .performance-top-message-assistant {
            margin-left: 15px;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-weight: 400;
            text-align: right;
            line-height: 20px;
            font-family: PingFangSC-Regular;
        }

        .performance-top-message-name {
            // width: 54px;
            // height: 25px;
            font-family: PingFangSC-Semibold;
            font-size: 16px;
            font-weight: 700;
            color: rgba(19, 20, 21, 1);
            text-align: left;
            margin-left: 8px;
        }

        .performance-top-message-people {
            // width: 112px;
            padding-left: 10px;
            padding-right: 10px;
            height: 20px;
            // background: linear-gradient(360deg, rgba(255, 245, 229, 1) 0%, rgba(255, 252, 246, 1) 100%);
            // border: 0.5px solid rgba(255, 88, 0, 1);
            // border-radius: 10px;
            // margin-left: 8px;
            margin-top: 6px;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-weight: 400;
            text-align: left;
            line-height: 20px;
            font-family: PingFangSC-Regular;
        }
    }

    .adm-tabs-tab {
        font-family: PingFangSC-Regular;
        font-size: 14px;
        font-weight: 400;
        color: rgba(102, 102, 102, 1);
        padding: 0;
    }

    .adm-tabs-tab-active {
        font-family: PingFangSC-Semibold;
        font-size: 14px;
        font-weight: 700;
        color: rgba(0, 198, 130, 1);
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        margin: 0;
        padding: 0;
        width: 48px;
        height: 28px;
        line-height: 28px;
        text-align: center;
    }

    .adm-tabs-tab-wrapper-stretch {
        flex: none !important;
        width: 48px;
        padding: 0 4px;
    }
}
.popup-title {
    border-radius: 0px 0px 6px 6px;
    background: #fff;

    .popup-bottom {
        .popup-bottom-p {
            height: 54.5px;
            border-bottom: 0.5px solid #f2f2f2;
            margin-right: 10px;
            margin-left: 10px;
            .adm-radio {
                display: flex;
                flex-direction: row-reverse;
                height: 100%;
                justify-content: space-between;
                align-items: center;
                .adm-radio-content {
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(51, 51, 51, 1);
                    padding-left: 16px;
                }
            }
            .adm-radio-checked {
                .adm-radio-icon {
                    background-color: rgba(0, 198, 130, 1);
                    color: #fff;
                    margin-right: 16px;
                }
            }
        }
    }
}


.placard-modal {
    width: 100%;
    height: 100%;
    z-index: 3;
    .adm-center-popup-body {
        --background-color: transparent;
    }
    .adm-center-popup-wrap {
        background-color: transparent;
    }
    .adm-modal-body,
    .adm-modal-content {
        max-height: 80vh;
        height: 80vh;
        padding: 0;
        border-radius: 0;
    }

    .placard-body {
        width: 100%;
        height: 100%;
        .adm-center-popup-body {
            --background-color: transparent;
        }
    }

    .placard-pic {
        display: block;
        max-width: 100%;
        max-height: calc(100% - 80px);
        margin: 0 auto;
    }

    .modal-tip {
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        color: rgba(255, 255, 255, 1);
        text-align: center;
        margin: 8px 0 24px 0;
    }

    .icon-close {
        display: block;
        width: 28px;
        height: 28px;
        margin: 0 auto;
        background: transparent url("#{$wos-cdn}rfgtv73c__w112_h112.png") no-repeat;
        background-size: cover;
    }
}

.real-account-mask-body {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 302px;
    height: 194px;
    margin-top: -97px;
    margin-left: -151px;
    background: white;
    border-radius: 20px;
    font-family: PingFangSC-Regular;

    .real-account-mask-message {
        color: #333333;
        font-size: 14px;
        line-height: 118px;
        text-align: center;
        height: 118px;
    }

    .real-account-mask-success-button {
        width: 260px;
        height: 46px;
        border-radius: 23px;
        background: rgba(0, 198, 130, 1);
        color: white;
        font-size: 16px;
        font-weight: 700;
        line-height: 46px;
        text-align: center;
        margin: 0 auto;
    }
}
