
export interface ILoanOverView {
    // 	本日营销客户数
    "todayApplicantCount": string;
    // 	本周营销客户数
    "weekApplicantCount": string;
    // 	本月营销客户数
    "monthApplicantCount": string;
    // 	本日通过营销客户数
    "todayApprovedApplicantCount": string;
    // 	本周通过营销客户数
    "weekApprovedApplicantCount": string;
    // 	本月通过营销客户数
    "monthApprovedApplicantCount": string;

    // // 	本日营销佣金
    "todayCommission": string;
    // 	本周营销佣金
    "weekCommission": string;
    // 	本月营销佣金
    "monthCommission": string;
    // 	营销总佣金
    "totalCommission": string;
}
// BIZ_STAFF业务员 TEAM_LEADER团队长 SALE_CHANNEL_LEADER
export type IUserType = "BIZ_STAFF" | "TEAM_LEADER" | "SALE_CHANNEL_LEADER" | "PARTTIME";

// 登录人信息
export interface ISalesInfo {
    "channelName": string;
    "leaderName": string;
    "salesStatus": string;
    "id": string;
    "phone": string;
    "salesType": IUserType;
    "isAuditPerson": boolean;
    "orgName": string;
    "platformHeaderUrl": string;
}

// 团队和团队成员
export interface ITeamOrTeamMember {
    "teamId": string;
    "salesId": string;
    "teamName": string;
    "teamLeaderId": string;
    "teamLeaderName": string;
    "salesName": string;
    "productName": string;
    "applicantCount": string;
    "approvedApplicantCount": string;
    "lineOfCredit": string;
}
// 团队和团队成员业绩请求参数
export interface ITeamOrTeamMemberParams {
    "pageNo": number;
    "pageSize": number;
    "sortField": string;//	排序字段(teamName-团队名称, teamLeaderName-团队长, productName-产品名称, applicantCount-申请客户数, approvedApplicantCount-审批通过客户数, lineOfCredit-授信额度, salesName-推广员姓名)
    "sortBy": string;//排序方式(0-降序，1-升序)
    "startTime": string;//startTime
    "endTime": string;//endTime
}
// 团队详情搜素请求参数
export interface ITeamMemberSearchParams{
    "pageNo": number;
    "pageSize": number;
    "sortField": string;//	排序字段(teamName-团队名称, teamLeaderName-团队长, productName-产品名称, applicantCount-申请客户数, approvedApplicantCount-审批通过客户数, lineOfCredit-授信额度, salesName-推广员姓名)
    "sortBy": string;//排序方式(0-降序，1-升序)
    "startTime": string;//startTime
    "endTime": string;//endTime
    "teamId":string;
    "productId":string;
}
// 个人业绩
export interface ISelfInfo {
    "id": string;
    "salesId": string;
    "productName": string;
    "applicantName": string;
    "applyTime": string;
    "applicantIdCode": string;
    "applicantMobile": string;
    "auditTime": string;
    "auditRemark": string;
    "lineOfCredit": string;
    "creditRate": string;
    "creditTerm": string;
    "repaymentType": string;
    "loanBalance": string;
    "isSupplement": number;
}
// 个人业绩查询参数
export interface ISelfInfoParams {
    "auditStatus": number;
    "pageNo": number;
    "pageSize": number;
    "startTime": string;
    "endTime": string;
}

// 业绩总览表头
export interface IPerformanceColumnItem {
    "name": string;
    "dataKey": string;
    "unit": string;
}
// 业绩总览数据信息
export interface IPerformanceInfo {
    "totalApplicantCount"?: number;
    "totalApprovedApplicantCount"?: number;
    "totalLineOfCredit"?: number;
    "totalLoanBalance"?: number;
}