import React, { FC, useEffect, useState } from "react";
import { RouteComponentProps } from "react-router-dom";
import { Radio, Popup, Modal } from "antd-mobile-v5";
import { Toast } from 'antd-mobile';
import { bridge } from "@sqb/utility";
import request from "api/request";
import { API } from "api/constants";
import "./index.scss";
import { IMG_WOS } from "../../constants";
import { formatProtocol } from "tools/utils/index";
import LoanType from "./LoanType";
import BottomBtn from "./LoanType/BottomBtn";

const My: FC<{ props: RouteComponentProps<{}> }> = (props) => {

    const [infomation, setInfoMation] = useState<any>({});
    const [productCategoryList, setProductCategoryList] = useState<any>([{}]);//（1-贷款）
    const [productCategoryVisible, setProductCategoryVisible] = useState(false)
    const [categoryValue, setCategoryValue] = useState<number>()
    const [placardVisible, setPlacardVisible] = useState<boolean>(false);
    const [placard, setPlacard] = useState<string>('');
    const [saleType, setSaleType] = useState<number>(0);//人员类型： 1-业务员、2-团队负责人、3-渠道负责人、4-BD、5-超管、6-专职、7-兼职
    const [assistantChooseVisible, setAssistantChooseVisible] = useState(false);
    const [assistantList, setAssistantList] = useState<any>([{}]);

    useEffect(() => {
        bridge.setTitle("我的业绩");
        getProductCategoryList();
        getConfig();
        getUser();
    }, []);

    const getConfig = () => {
        request.get(API.queryJudgeCondition).then((res) => {
            const { rCode, data } = res || {};
            if (rCode === 0) {
                const { saleType } = data;
                setSaleType(saleType);
            }
        });
    };
    const getProductCategoryList = () => {
        request.get(API.queryMyCategoryType).then((res) => {
            const { rCode, data, rMsg } = res || {};
            if (rCode === 0) {
                if (data.length > 0) {
                    setProductCategoryList(data)
                    setCategoryValue(data[0].productCategoryCode)
                } else {
                    return
                }
            } else {
                Toast.show(rMsg || '系统错误,请稍后重试');
            }
        });
    };


    const getUser = () => {
        request.get(API.queryUserinfo).then((res) => {
            const { rCode, data, rMsg } = res || {};
            if (rCode === 0) {
                setInfoMation(data);
            } else {
                Toast.info(rMsg || '系统错误,请稍后重试');
            }

        });
    };

    const getTeamUser = () => {
        const param = { pageNo: 1, pageSize: 100 }
        request.get(API.queryTeamMemberByPage, param).then((res) => {
            const { rCode, data, rMsg } = res || {};
            if (rCode === 0 && data) {
                console.log(data)
                setAssistantList(data.list);
                setAssistantChooseVisible(true);
            } else {
                Toast.info(rMsg || '系统错误,请稍后重试');
            }

        });
    };

    const saveOrUpdateAssistant = (id) => {
        request.post(API.saveOrUpdateAssistant, { id }).then((res) => {
            const { rCode, rMsg } = res || {};
            if (rCode === 0) {
                getUser();
                setAssistantChooseVisible(false);
            } else {
                Toast.info(rMsg || '系统错误,请稍后重试');
            }

        });
    }

    const popupCategoryAction = (val) => {
        setCategoryValue(val)
        setProductCategoryVisible(false)
    }
    const categoryValueMethod = () => {
        let str = ''
        productCategoryList.forEach(item => {
            if (item.productCategoryCode === categoryValue) {
                str = item.productCategoryName
            }
        })
        return str
    }
    const shareAction = async () => {
        const res = await request.get(API.getPosterUrlOfInviteUser);
        const { rCode, data, rMsg } = res || {};
        if (rCode !== 0 || !data?.posterUrl) {
            Toast.show(rMsg || '系统错误,请稍后重试');
            return;
        }

        setPlacard(data.posterUrl);
        setPlacardVisible(true);
    }
    const handleClosePlacardModal = () => {
        setPlacardVisible(false);
    }
    return (
        <>

            <div className="performance" >

                {
                    typeof (categoryValue) === "number" && <div className="performance-all">
                        <div className="performance-top-content">
                            <div className="performance-top">
                                <div className="performance-top-img">
                                    <img
                                        src={
                                            infomation.userImg === ""
                                                ? `${IMG_WOS}6cd2i5bf__w200_h200.png`
                                                : infomation.userImg
                                        }
                                    />
                                </div>
                                <div className="performance-top-message">
                                    <div className="performance-top-message-name">
                                        {infomation.saleName}
                                         <span className="performance-top-message-assistant"
                                          onClick={() => {
                                            getTeamUser();
                            }}>（备调：{infomation.assistantName}）</span>
                                    </div>
                                    <div className="performance-top-message-people">
                                        上级负责人：{infomation.leaderName}
                                    </div>
                                </div>
                            </div>
                            <div className="performance-top-content-right" onClick={() => {

                                if (productCategoryList.length <= 1) { return }
                                setProductCategoryVisible(true)
                            }}>
                                <div className="performance-top-content-button">{categoryValueMethod()}</div>
                                <img className="performance-top-content-image" src="//j1.58cdn.com.cn/jinrong/images/ems17186933812341693fca622145.png" alt="" />
                            </div>
                            <Popup
                                position='top'
                                visible={productCategoryVisible}
                                onMaskClick={() => setProductCategoryVisible(false)}
                            >
                                <div className='popup-title'>
                                    <div className='popup-bottom'>
                                        <Radio.Group defaultValue={categoryValue} onChange={(val) => { popupCategoryAction(val) }}>
                                            {
                                                productCategoryList.map(item => {
                                                    return <div key={item.productCategoryCode} className='popup-bottom-p'>
                                                        <Radio className='popup-bottom-p-radio' value={item.productCategoryCode} > {item.productCategoryName}  </Radio>
                                                    </div>
                                                })
                                            }

                                        </Radio.Group>
                                    </div>
                                </div>
                            </Popup>
                            <Popup
                                position='top'
                                visible={assistantChooseVisible}
                                onMaskClick={() => setAssistantChooseVisible(false)}
                            >
                                <div className='popup-title'>
                                    <div className='popup-bottom'>
                                        <Radio.Group defaultValue={categoryValue} onChange={(val) => { saveOrUpdateAssistant(val) }}>
                                            {
                                                assistantList.map(item => {
                                                    return <div key={item.id} className='popup-bottom-p'>
                                                        <Radio className='popup-bottom-p-radio' value={item.id} > {item.saleName}  </Radio>
                                                    </div>
                                                })
                                            }

                                        </Radio.Group>
                                    </div>
                                </div>
                            </Popup>
                        </div>
                        <LoanType />
                    </div>

                }


            </div>
            {
                saleType === 7 ? <div className="invite-newmember-back">
                    <BottomBtn onInvite={shareAction}></BottomBtn>
                </div> : null
            }

            <Modal
                visible={placardVisible}
                className="placard-modal"
                content={
                    <div className="placard-body">
                        <img
                            className="placard-pic"
                            src={formatProtocol(placard)}
                            alt="海报"
                        />
                        <p className="modal-tip">长按保存或分享海报</p>
                        <i
                            className="icon-close"
                            onClick={() => handleClosePlacardModal()}
                        ></i>
                    </div>
                }
            >
            </Modal>


        </>
    );
};
export default My;

