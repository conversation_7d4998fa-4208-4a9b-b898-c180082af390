import React, { useState, useEffect, FC } from 'react';
import dayjs from 'dayjs';
import { CalendarPickerView, Popup, Button, Toast } from 'antd-mobile-v5';
import DayWeekMonth, { dataList } from '../DayWeekMonth';
import "./index.scss";

// defaultRange 去年今天到今天

const defaultRange: [Date, Date] = [
    dayjs().subtract(1, 'year').toDate(),
    new Date()
]

// 本日
const todayDate: [Date, Date] = [
    new Date(),
    new Date(),
]
// 本周日期
const weekDate = [dayjs().day(1).toDate(), dayjs().toDate()]
// 本月日期=》 今天
const monthDate = [dayjs().startOf('month').toDate(), dayjs().toDate()]

// 本年=》今天
const yearDate = [dayjs().startOf('year').toDate(), dayjs().toDate()]

const dateMap = {
    "1": todayDate,
    "2": weekDate,
    "3": monthDate,
    "4": yearDate
}
// 快捷选项默认值
const defaultWeekDayMonthKey = "1";


interface IProps {
    dateValue: [Date, Date] | null;
    dateKey: string;
    visible?: boolean;
    onConfirm?: (params: {
        date: [Date, Date],
        weekDayMonth?: string
        weekDayMonthKey?: string
    }) => void;
    setVisible: (params: boolean) => void;
}
const NDcalenderPicker: FC<IProps> = ({ dateValue, dateKey, visible = false, onConfirm, setVisible }) => {
    const [value, setValue] = useState<[Date, Date] | null>(dateValue || todayDate)
    // 日 周 月 年
    const [activeKey, setActiveKey] = useState(dateKey || defaultWeekDayMonthKey);
    // ref
    const ref = React.useRef(null);
    // 是否第一次渲染
    // const [firstRender, setFirstRender] = useState(true);

    // useEffect(() => {
    //     if (visible && firstRender) {
    //         Promise.resolve().then(() => {
    //             scrollIntoView()
    //         })
    //     }

    // }, [visible, firstRender]);

    useEffect(() => {
        if (visible) {
            const valueTemp = dateValue || todayDate;
            setValue(valueTemp);

            setActiveKey(dateKey || (dateValue ? "" : defaultWeekDayMonthKey));

            Promise.resolve().then(() => {
                scrollIntoView(valueTemp);
            })
        }
    }, [visible, dateValue, dateKey]);

    const scrollIntoView = (value, behavior: ScrollBehavior = "auto") => {
        const elements = document.querySelectorAll('.adm-calendar-picker-view-title');
        const elementsArr = Array.from(elements);
        
        const year = dayjs(value[0]).toDate().getFullYear();
        const month = dayjs(value[0]).toDate().getMonth() + 1;

        elementsArr.some((item) => {
            if (item.textContent === `${year}年${month}月`) {

                // setFirstRender(false);
                item.parentElement?.scrollIntoView({
                    behavior: behavior,
                })
                return true;
            }
            return false;
        })

    };

    const onSubmit = () => {
        if (!value) {
            Toast.show({
                content: "请选择日期",
                duration: 2000,
            });
            return
        }
        onConfirm && onConfirm({
            date: value,
            weekDayMonth: dataList.find(item => item.key === activeKey)?.title,
            weekDayMonthKey: activeKey
        });
        setVisible(false);
    }
    return (
        <div>
            <Popup
                className='ndcalender_picker'
                visible={visible}
                bodyStyle={{
                    minHeight: '70vh',
                    borderRadius: '12px 12px 0 0',
                }}
                onMaskClick={() => { setVisible(false) }}
            >
                <div className="ndcalender_picker_container">
                    <CalendarPickerView
                        ref={ref}
                        min={defaultRange[0]}
                        max={defaultRange[1]}
                        value={value}
                        selectionMode='range'
                        title={(<div className="ndcalender_picker_container_header">
                            <span>日期选择</span>
                            <img onClick={() => { setVisible(false) }} src="//fhlui1001.58wos.com.cn/cDazYxWcDHJ/picasso/vgq8qsuc__w56_h56.png" />
                        </div>)}
                        onChange={val => {
                            setValue(val);
                            setActiveKey("");
                        }}
                    />

                    <div className="ndcalender_picker_container_btn_box">
                        <DayWeekMonth activeKey={activeKey} onChange={(e) => {
                            setActiveKey(e);
                            setValue(dateMap[e]);
                            scrollIntoView(dateMap[e], "smooth");
                        }}></DayWeekMonth>
                        <Button onClick={onSubmit} className='ndcalender_picker_container_btn'>确定</Button>
                    </div>
                </div>

            </Popup>

        </div>
    )
}

export default NDcalenderPicker