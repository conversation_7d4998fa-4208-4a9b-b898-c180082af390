@import "~scss/mixins/px2rem";

.ndcalender_picker {
    &.adm-popup {
        --adm-color-background: #ffffff;
        --adm-color-primary: #00c682;
        --adm-color-white: #ffffff;
        --adm-color-light: #ccc;
        --adm-font-size-1: 10px;
        --adm-font-size-2: 10px;
        --adm-font-size-3: 11px;
        --adm-font-size-4: 12px;
        --adm-font-size-5: 12px;
        
        --adm-font-size-6: 12px;
        --adm-font-size-7: 13px;
        --adm-font-size-8: 14px;
        --adm-font-size-9: 17px;
        --adm-font-size-10: 18px;
        // --adm-color-box: #f5f5f5;
        --adm-color-box: #ffffff;
        --adm-color-border: #eee;
        --adm-color-text-light-solid: #fff;
        --adm-color-light: #ccc;
    }

    &_container {
        @supports (bottom: env(safe-area-inset-bottom)) {
            padding-bottom: env(safe-area-inset-bottom);
        }

        &_header{
            font-family: PingFangSC-Medium;
            font-size: 20px;
            font-weight: 500;
            display:flex;
            align-items: center;
            justify-content: space-between;
        }

        .adm-calendar-picker-view {

            .adm-calendar-picker-view-header {
                padding: rem(50) rem(32) rem(24);
                border-bottom: 0;
            }
            .adm-calendar-picker-view-mark {
                font-family: PingFangSC-Regular;
                padding: 0 rem(32);
                border-bottom: 0;
            }

            .adm-calendar-picker-view-body {
                height: 52vh;

                .adm-calendar-picker-view-title {
                    font-family: PingFangSC-Semibold;
                    padding: rem(8) rem(32);
                }
            }
            .adm-calendar-picker-view-cells {
                padding: rem(8) rem(32);
            }
            .adm-calendar-picker-view-cell-selected {
                background-color: rgba(0, 198, 130, 0.1) !important;
            }
            .adm-calendar-picker-view-cell-selected-end,
            .adm-calendar-picker-view-cell-selected-begin {
                background-color: rgba(0, 198, 130, 1) !important;
            }
            .adm-calendar-picker-view-cell-selected-begin {
                border-top-left-radius: rem(16) !important;
                border-bottom-left-radius: rem(16) !important;
            }
            .adm-calendar-picker-view-cell-selected-end {
                border-top-right-radius: rem(16) !important;
                border-bottom-right-radius: rem(16) !important;
            }
            
            .adm-calendar-picker-view-cell-top {
                font-family: PingFangSC-Regular;
            }
            .adm-calendar-picker-view-cell-date {
                font-weight: 500;
            }
        }
        
        // overflow-y: auto;
        .adm-button {
            font-size: rem(34);
            display: block;
            height: rem(96);
            border-radius: rem(48);
            background-color: #00c682;
            width: rem(650);
            color: white;
            margin: 0 auto;
            // margin-top: rem(20);
            // margin-bottom: rem(20);
        }
        &_btn_box {
            padding-bottom: rem(20);
        }
        &_btn {
        }
    }
}
