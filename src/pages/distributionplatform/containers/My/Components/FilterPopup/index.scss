@import "~scss/mixins/px2rem";
@import "~scss/index";

.dis-filter-popup-container {

    .dis-filter-popup {
        border-radius: rem(16) rem(16) 0 0;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        
        @supports (bottom: constant(safe-area-inset-bottom)) {
            padding-bottom: constant(safe-area-inset-bottom);
        }
        @supports (bottom: env(safe-area-inset-bottom)) {
            padding-bottom: env(safe-area-inset-bottom);
        }
    }
    
    .dis-filter-popup-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: rem(50) rem(32) 0;
        font-family: PingFangSC-Medium;
        font-size: rem(40);
        font-weight: 500;
        line-height: rem(56);
    }

    .dis-filter-popup-close {
        width: rem(56);
        height: rem(56);
        background: url(#{$wos-cdn-1}g0fbcpes__w56_h56.png) no-repeat center;
        background-size: cover;
    }

    .dis-filter-popup-content {
        padding: rem(60) rem(30) 0;
        flex: 1;
        overflow-y: auto;
    }

    .dis-filter-form-item {
        margin-bottom: rem(42);
    }

    .dis-filter-form-item-title {
        font-family: PingFangSC-Medium;
        font-size: rem(28);
        font-weight: 500;
        line-height: rem(40);
        color: #333;
        margin-bottom: rem(24);
    }

    .dis-filter-form-input {
        font-family: PingFangSC-Regular;
        font-size: rem(28);
        font-weight: 400;
        line-height: rem(80);
        color: #85858C;
        display: flex;

        input {
            height: rem(80);
            padding: 0 rem(40);
            background-color: rgba(249, 249, 249, 1);
            border-radius: rem(40);
            color: #333;
            border: none;
            outline: none;
            margin-right: rem(24);
            flex: 1;

            &::placeholder {
                color: #999;
            }
        }
    }

    .dis-filter-form-radio {
        display: flex;

        li {
            width: rem(208);
            height: rem(80);
            background-color: rgba(249, 249, 249, 1);
            border-radius: rem(54);
            font-family: PingFangSC-Regular;
            font-size: rem(28);
            font-weight: 400;
            line-height: rem(80);
            color: #333;
            text-align: center;
            margin-right: rem(24);

            &.active {
                background-color: rgba(0, 198, 130, 0.16);
                color: #00C682;
            }
        }
    }

    .dis-filter-actions {
        margin-top: rem(300);
        padding: rem(16) 0;
        display: flex;
        justify-content: space-around;
    }

    .dis-filter-btn {
        width: 50%;
        height: rem(96);
        border: rem(2) solid #00C682;
        border-radius: rem(72);
        font-family: PingFangSC-Medium;
        font-size: rem(32);
        font-weight: 500;
        line-height: rem(96);
        color: #00C682;
        text-align: center;

        &.dis-filter-btn-confirm {
            background: #00C682;
            color: #fff;
            margin-left: rem(24);
        }
    }
}