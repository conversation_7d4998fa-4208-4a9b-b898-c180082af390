import React, { useState, Dispatch, FC, SetStateAction, memo } from 'react';
import { Popup } from 'antd-mobile-v5';
import './index.scss';

interface IProps {
    onChange: (value) => void,
    height?: string,
    visible: boolean,
    setVisible: Dispatch<SetStateAction<boolean>>,
}

const FilterOverdueEnum = [
    {
        value: 1,
        label: '逾期'
    },
    {
        value: 0,
        label: '未逾期'
    },
]

const FilterPopup: FC<IProps> = ({ onChange, visible, setVisible, ...props }) => {

    const [name, setName] = useState<string>('');
    const [overdue, setOverdue] = useState<number | null>(null);
    const [overdueLabel, setOverdueLabel] = useState<string>('');

    const handleChangeName = (e) => {
        setName(e.target.value);
    }

    const handleChangeOverdue = (item) => {
        setOverdue(item.value);
        setOverdueLabel(item.label);
    }

    const handleClear = () => {
        setName('');
        setOverdue(null);
        setOverdueLabel('');
    }
    
    const handleConfirm = () => {
        onChange({
            name,
            overdue,
            overdueLabel
        })
    }

    return (
        <Popup
            className='dis-filter-popup-container'
            bodyClassName="dis-filter-popup"
            visible={visible}
            onMaskClick={() => {
                setVisible(false)
            }}
            onClose={() => {
                setVisible(false)
            }}
            // destroyOnClose={true}
            {...props}
        >
            <div className="dis-filter-popup-title">
                <div className="dis-filter-popup-title-text">
                    其他条件查询
                </div>
                <div className="dis-filter-popup-close" onClick={() => {
                    setVisible(false)
                }}></div>
            </div>
            <div className='dis-filter-popup-content'>
                <div className="dis-filter-form-item">
                    <div className="dis-filter-form-item-content">
                        <div className="dis-filter-form-input">
                            <input
                                type="text"
                                placeholder='输入姓名或身份证后4位'
                                value={name}
                                onInput={handleChangeName}
                                onChange={handleChangeName}
                            />
                            <span onClick={() => setName('')}>取消</span>
                        </div>
                    </div>
                </div>
                <div className="dis-filter-form-item">
                    <div className="dis-filter-form-item-title">
                        是否逾期
                    </div>
                    <div className="dis-filter-form-item-content">
                        <ul className='dis-filter-form-radio'>
                            {FilterOverdueEnum.map((item, i) => (
                                <li
                                    className={overdue === item.value && 'active' || ''}
                                    key={`dis-filter-form-radio${i}`}
                                    onClick={() => handleChangeOverdue(item)}
                                >{item.label}</li>
                            ))}
                        </ul>
                    </div>
                </div>

                <div className="dis-filter-actions">
                    <div className="dis-filter-btn" onClick={handleClear}>
                        清除
                    </div>
                    <div className="dis-filter-btn dis-filter-btn-confirm" onClick={handleConfirm}>
                        确认
                    </div>
                </div>
            </div>
        </Popup>
    )
}

export default memo(FilterPopup);