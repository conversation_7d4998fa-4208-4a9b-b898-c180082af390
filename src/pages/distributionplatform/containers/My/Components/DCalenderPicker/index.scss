@import "~scss/mixins/px2rem";
.dis_my_calender_box {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    padding: rem(15);
    border-radius: rem(16);
    margin-left: rem(16);
    img {
        width: rem(34.5);
    }
}
.dis_my_calender {
    --adm-color-primary: #00c682;
    --adm-color-white: white;
    --adm-font-size-8: 16px;
    --adm-font-size-1: 9px;
    --adm-color-box: #f5f5f5;
    --adm-color-border: #eee;
    --adm-color-text-light-solid: #fff;
    --adm-color-light: #ccc;
    .adm-calendar-picker-view-cell-selected {
        background-color: rgba(0, 198, 130, 0.1) !important;
    }
    .adm-calendar-picker-view-cell-selected-end,
    .adm-calendar-picker-view-cell-selected-begin {
        background-color: rgba(0, 198, 130, 1) !important;
    }
    .adm-popup-body {
        background-color: #ffffff;
    }
    .adm-calendar-picker-footer-bottom {
        .adm-button {
            height: 44px;
            border-radius: 20px;
        }
    }
}
