import React, { useState, useRef, useEffect } from 'react';
import { CalendarPicker, CalendarPickerRef } from 'antd-mobile-v5';
import dayjs from "dayjs";

import "./index.scss";

const year = dayjs().toDate().getFullYear();
const month = dayjs().toDate().getMonth() + 1;
const DCalendarPicker = ({ onConfirm, ...props }) => {
    const [visible, setVisible] = useState(false);
    // 是否第一次渲染
    const [firstRender, setFirstRender] = useState(true);
    const ref = useRef<CalendarPickerRef>(null);
    useEffect(() => {

        if (visible && firstRender) {
            const elements = document.querySelectorAll('.adm-calendar-picker-view-title');
            const elementsArr = Array.from(elements)
            let scrollToElement: any = null;
            Promise.resolve().then(() => {
                elementsArr.some((item) => {
                    if (item.textContent === `${year}年${month}月`) {
                        scrollToElement = item;
                        setFirstRender(false);
                        item.scrollIntoView()
                        return true;
                    }
                    return false;
                })
            })

        }

        //
    }, [visible, firstRender])
    return (
        <div className='dis_my_calender_box' onClick={() => setVisible(true)}>
            <img src="//wos.58cdn.com.cn/cDazYxWcDHJ/picasso/hn45mtdb__w80_h80.png" />
            <CalendarPicker
                ref={ref}
                popupClassName='dis_my_calender'
                visible={visible}
                defaultValue={[
                    dayjs().toDate(),
                    dayjs().toDate()
                ]}
                selectionMode='range'
                onClose={() => setVisible(false)}
                onMaskClick={() => setVisible(false)}
                onConfirm={(val) => {
                    onConfirm(val)
                }}
                onChange={val => {
                    props.onChange && props.onChange(val)
                    console.log(val);
                }}
                {...props}
            />
        </div>
    )
}

export default DCalendarPicker;