@import "~scss/mixins/px2rem";
@import "~scss/index";

.dis-additional-remarks-popup-container {

    .dis-additional-remarks-popup {
        background-color: #fff;
    }
    
    .dis-additional-remarks-popup-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: rem(50) rem(32) 0;
        font-family: PingFangSC-Medium;
        font-size: rem(40);
        font-weight: 500;
        line-height: rem(56);
    }

    .dis-additional-remarks-popup-close {
        width: rem(56);
        height: rem(56);
        background: url(#{$wos-cdn-1}g0fbcpes__w56_h56.png) no-repeat center;
        background-size: cover;
    }

    .dis-additional-remarks-popup-content {

        @supports (bottom: env(safe-area-inset-bottom)) {
            padding-bottom: env(safe-area-inset-bottom);
        }
    }

    .additonal_remarks_box {
        height: 70vh;
        background-color: #fff;

        .form_container {
            .f_title {
                font-size: rem(32);
            }
        }
    }

    // popup组件有fixed和transform属性，导致弹窗内的fixed元素定位出错，手动调整
    // 安卓这样可以，ios不行，改为js处理
    // .upload_image_container {
    //     .rem_upload .adm-mask {
    //         // height: 100vh;
    //         // top: -20vh;
    //     }
    // }

    // ios13.5版本，fixed元素定位出错，手动调整
    &.ios13-5 {
        .additonal_remarks_box {
            padding-bottom: 0;

            .bottom_button {
                position: static;
                padding: rem(48) 0 0;
            }
        }
    }
}
