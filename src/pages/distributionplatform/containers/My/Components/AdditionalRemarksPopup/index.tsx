import React, { FC, memo } from 'react';
import { Popup } from 'antd-mobile-v5';
import AdditionalRemarks from '../../../AdditionalRemarks';
import './index.scss';

interface IProps {
    id: string,
    visible: boolean,
    onCloseFn: Function,
    onSubmitCb: Function,
}

// 判断当前是不是ios特定版本
function getIosV() {
    const ua = navigator.userAgent;
    const ios = !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    const version = ua.match(/OS (\d+)_(\d+)_?(\d+)?/);

    if (ios && version) {
        const [, major, minor] = version;

        return `ios${major}-${minor}`;
    }
    return '';
}

const AdditionalRemarksPopup: FC<IProps> = ({
    id, visible, onCloseFn, onSubmitCb, ...props
}) => {

    const iosV = getIosV();
    const onScrollTop = () => {
        document.querySelector('.dis-additional-remarks-popup-content')?.scrollTo(0, 0);
    }

    // 处理popup中upload组件图片预览时展示bug
    // popup组件有transform属性，导致弹窗内的fixed元素根据父元素定位，手动调整
    const togglePreviewCb = (flag: boolean) => {
        getIosV();
        const popupBody = document.querySelector('.dis-additional-remarks-popup') as HTMLElement;
        const popupContentBox = document.querySelector('.additonal_remarks_box') as HTMLElement;

        if (popupBody) {
            popupBody.style.transform = flag ? 'none' : '';
        }

        if (popupContentBox) {
            popupContentBox.style.overflowY = flag ? 'initial' : '';
        }
    }

    return (
        <Popup
            className={`dis-additional-remarks-popup-container ${iosV}`}
            bodyClassName="dis-additional-remarks-popup"
            visible={visible}
            onMaskClick={() => {
                onCloseFn();
            }}
            onClose={() => {
                onCloseFn();
            }}
            bodyStyle={{
                // height: '80vh',
                borderTopLeftRadius: '8px',
                borderTopRightRadius: '8px',

            }}
            afterClose={onScrollTop}
            // 移除destroyOnClose以保持组件状态
            {...props}
        >
            <div className="dis-additional-remarks-popup-title">
                <div className="dis-additional-remarks-popup-title-text">
                    补充说明
                </div>
                <div
                    className="dis-additional-remarks-popup-close"
                    onClick={() => onCloseFn()}
                ></div>
            </div>
            <div className='dis-additional-remarks-popup-content'>
                <AdditionalRemarks
                    id={id}
                    onSubmitCb={onSubmitCb}
                    onTogglePreviewCb={togglePreviewCb}
                />
            </div>
        </Popup>
    )
}

export default memo(AdditionalRemarksPopup);