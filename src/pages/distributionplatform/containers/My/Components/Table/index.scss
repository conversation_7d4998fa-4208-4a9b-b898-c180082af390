@import "~scss/mixins/px2rem";

.my_table_container {
    position: relative;
    padding: 0 12px;
}
.my_table_box {
    max-width: 100vw;
    overflow-x: scroll;
    overflow-y: hidden;
    // 隐藏滚动条
    scrollbar-width: thin; /* 设置滚动条宽度 */
    scrollbar-color: transparent transparent; /* 设置滚动条颜色 */
    table,
    th,
    td {
        font-size: rem(24);
        border-collapse: collapse;
        border-bottom: solid 1px #eeeeee;
        background-color: white;
        box-sizing: border-box;
        text-align: left;
    }
    tr {
        &.isLight td {
            background-color: #ffb6b3;
        }
    }
    th {
        color: #999999;
        min-width: rem(165);
        padding: rem(24);
        height: rem(80);
        font-weight: 400;

        .arrow_flex_box {
            display: flex;
            align-items: center;
        }
    }
    td {
        height: rem(108);
        padding: rem(24);
        padding-right: 0;
    }
    .table-cell-fix-left-last::after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: -1px;
        width: 30px;
        transform: translate(100%);
        content: "";
        pointer-events: none;
        box-shadow: inset 10px 0 8px -8px #00000026;
    }

    .table-cell-fix-right-last::before {
        position: absolute;
        top: 0;
        left: -32px;
        bottom: -1px;
        width: 30px;
        transform: translate(100%);
        content: "";
        pointer-events: none;
        box-shadow: inset 10px 0 8px -8px #00000026;
    }
}
