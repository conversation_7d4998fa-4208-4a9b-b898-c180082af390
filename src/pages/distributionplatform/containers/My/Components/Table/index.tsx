import React, { FC, ReactNode, useEffect, useRef,memo } from 'react';
import SortArrow from './SortArrow';
import Empty from './Empty';
import './index.scss';

interface IProps {
    columns: {
        title: string;
        dataIndex: string;
        fixed?: string;
        key: string;
        sortable?: boolean;
        render?: (a: any, b: any) => string | ReactNode;
        [propName: string]: any
    }[],
    dataSource: { [propName: string]: any }[],
    onSort?: (dataIndex: string, sortBy) => void
}

const Table: FC<IProps> = ({ columns = [], dataSource = [], onSort }) => {
    const tableRef = useRef<any>();
    const scrollBoxRef = useRef<HTMLDivElement>(null);
    // 升序将序
    const sortMapRef = useRef<any>({})
    useEffect(() => {
        setTdThFixed();
        setTdFixed();
    }, [columns, dataSource]);
    useEffect(() => {
        columns.forEach(item => {
            if (item.sortable) {
                // 0 降序 1 升序
                sortMapRef.current[item.dataIndex] = 1;
            }
        })
    }, [columns])

    // 设置单元格固定
    const setTdThFixed = () => {
        const tHead = tableRef.current?.children[0];
        const trs = tableRef.current?.children[0].children;;
        const trsArr = Array.from(trs);
        const parentWidth = tHead.offsetWidth;
        // const th: any = trsArr[0];
        // const tds = th.children;
        let scrollLeft = scrollBoxRef.current?.scrollLeft || 0;
        trsArr.forEach((tr: any, index) => {
            const tds = tr.children;
            const tdsArr = Array.from(tds);
            let rightFixed = false;
            tdsArr.forEach((td: any, index) => {
                const column = columns[index];
                const clientRect = td.getBoundingClientRect();
                // console.log(td.style.left, parentWidth, "td.style.left");
                if (column.fixed === "left" && !td.style.left) {
                    td.style.left = clientRect.left - 12 + scrollLeft + "px";
                    td.style.position = "sticky";
                    td.style.zIndex = 1;
                    td.className = "table-cell-fix-left-last"
                } else if (column.fixed === "right" && !td.style.right) {
                    td.style.position = "sticky";
                    td.style.right = (parentWidth - clientRect.width - td.offsetLeft + 12) + "px";
                    td.style.zIndex = 1;
                    if (!rightFixed) {
                        td.className = "table-cell-fix-right-last";
                        rightFixed = true;
                    }
                }
            })
        })

    }

    // 设置单元格固定
    const setTdFixed = () => {
        const tbody = tableRef.current?.children[1];
        const trs = tbody.children;
        const trsArr = Array.from(trs);
        const parentWidth = tbody.offsetWidth;

        let scrollLeft = scrollBoxRef.current?.scrollLeft || 0;
        trsArr.forEach((tr: any, index) => {
            const tds = tr.children;
            const tdsArr = Array.from(tds);
            let rightFixed = false;
            tdsArr.forEach((td: any, index) => {
                const column = columns[index];
                const clientRect = td.getBoundingClientRect();
                // console.log(td.style.left, "td.style.left");
                if (column.fixed === "left" && !td.style.left) {
                    td.style.left = clientRect.left - 12 + scrollLeft + "px";
                    td.style.position = "sticky";
                    td.style.zIndex = 1;
                    td.className = "table-cell-fix-left-last"
                } else if (column.fixed === "right" && !td.style.right) {
                    td.style.position = "sticky";
                    td.style.right = (parentWidth - clientRect.width - td.offsetLeft + 12) + "px";
                    td.style.zIndex = 1;
                    if (!rightFixed) {
                        td.className = "table-cell-fix-right-last";
                        rightFixed = true;
                    }
                }
            })
        })

    }
    const sortFn = (dataIndex) => {
        onSort && onSort(dataIndex, sortMapRef.current[dataIndex])
        sortMapRef.current[dataIndex] = sortMapRef.current[dataIndex] === 1 ? 0 : 1;

    }
    return (
        <div className="my_table_container">
            <div className='my_table_box' ref={scrollBoxRef}>
                <table ref={tableRef}>
                    <thead>
                        <tr>
                            {
                                columns.map((item, index) => {
                                    return <th key={item.key}><div className="arrow_flex_box">
                                        {item.title}
                                        {
                                            item.sortable ? <SortArrow onSort={sortFn.bind(null, item.dataIndex)} /> : null

                                        }
                                    </div>
                                    </th>
                                })
                            }

                        </tr>
                    </thead>
                    <tbody>
                        {
                            dataSource.length > 0 && dataSource.map((info, infoIndex) => {
                                return <tr
                                    key={infoIndex}
                                    className={info.isSupplement === 1 ? 'isLight' : ''}
                                >
                                    {
                                        columns.map((item, index) => {
                                            return <td key={item.key}>
                                                {item.render
                                                    ? item.render(info[item.dataIndex], info)
                                                    : info[item.dataIndex]
                                                }
                                            </td>
                                        })
                                    }
                                </tr>
                            })
                        }
                    </tbody>
                </table>
        
            </div >
            {
                dataSource.length <= 0 && <Empty />
            }
        </div>

    )
}

export default memo(Table);