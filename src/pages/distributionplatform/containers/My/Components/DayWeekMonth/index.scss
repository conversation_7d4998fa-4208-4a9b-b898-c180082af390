@import "~scss/mixins/px2rem";

.dis_day_week_month {
    display: flex;
    font-size: rem(28);
    color: #666666;
    padding: rem(4);
    border-radius: rem(16);
    // box-sizing: border-box;
    &_item {
        width: rem(124);
        height: rem(64);
        border-radius: rem(32);
        box-sizing: border-box;
        background-color: #f9f9f9;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: rem(17);
        flex: 1;
    }
    .active {
        background-color: rgba(0, 198, 130, 0.16);
        color: #00c682;
    }
}
.dis_day_week_month_container {
    display: flex;
    font-family: PingFangSC-Regular;
    align-items: center;
    width: 100%;
    padding-left: rem(32);
    margin-bottom: rem(32);

}
