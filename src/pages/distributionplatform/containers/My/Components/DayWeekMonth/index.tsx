/*
 * @Author: z<PERSON>wu<PERSON> <EMAIL>
 * @Date: 2024-10-25 17:11:18
 * @LastEditors: zhangwu01 <EMAIL>
 * @LastEditTime: 2024-12-04 16:11:06
 * @Description: 本日 本周 本月的tag按钮
 * 
 */
import React from 'react';
import "./index.scss";

export const dataList = [
    {
        title: "本日",
        key: "1"
    },
    {
        title: "本周",
        key: "2"
    },
    {
        title: "本月",
        key: "3"
    },
    {
        title: "本年",
        key: "4"
    }
]

const DayWeekMonth = ({ activeKey, onChange }) => {
    return (
        <div className='dis_day_week_month_container'>
            <span>快捷选项</span>
            <div className='dis_day_week_month'>
                {
                    dataList.map(item => {
                        return (
                            <div onClick={() => onChange(item.key)} key={item.key} className={item.key === activeKey ? "dis_day_week_month_item active" : "dis_day_week_month_item"}>
                                {item.title}
                            </div>
                        )
                    })
                }
            </div>
        </div>

    )
}

export default DayWeekMonth;