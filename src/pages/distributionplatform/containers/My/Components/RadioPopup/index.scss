@import "~scss/mixins/px2rem";

.dis_radio_popup_container {
    &_box {
        padding: 0 rem(32);
        @supports (bottom: env(safe-area-inset-bottom)) {
            padding-bottom: env(safe-area-inset-bottom);
        }
        --adm-color-primary: #00c682;
        --adm-color-text-light-solid: white;
        --adm-color-light: #ccc;
        .dis-radio-icon {
            flex: none;
            width: rem(40);
            border-radius: rem(40);
            box-sizing: border-box;
            height: rem(40);
            color: var(--adm-color-text-light-solid);
            background-color: #00c682;
        }
    }
    &_title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: rem(54);
        font-family: PingFangSC-Medium;
        margin-bottom: rem(24);
        font-size: rem(40);

        &_close img {
            display: block;
            width: rem(56);
            height: rem(56);
        }
    }
    &_content {
        overflow-y: scroll;
        &_item {
            box-sizing: border-box;
            height: rem(112);
            font-family: PingFangSC-Regular;
            font-size: rem(30);
            display: flex;
            justify-content: space-between;
            padding: rem(32) rem(18);
            border-bottom: 1px solid rgba(242, 242, 242, 1);
        }
    }
}
.dis_radio_popup {
    background-color: #ffffff;
}
