@import "~scss/mixins/px2rem";

.lt_tabs {
    &_title_box {
        display: flex;
        justify-content: flex-start;
        font-family: PingFangSC-Semibold;
        font-size: rem(36);
        font-weight: 700;
        position: relative;
        margin-bottom: rem(10);
        padding-bottom: rem(16);
        &_line {
            width: rem(100);
            height: rem(6);
            border-radius: rem(3);
            position: absolute;
            bottom: rem(10);
            transition:left .3s ;
            img {
                width: rem(48);
                height: rem(18);
                display: block;
                margin: 0 auto;
                margin-top: rem(-5);
            }
        }
        &_wrapper {
            display: flex;
            justify-content: flex-start;
        }
    }
    &_title {
        padding: rem(16) rem(24);
        &_active {
            color: #00c682;
        }
    }
    &_content {
        display: none;
        &_active {
            display: block;
        }
    }
}
