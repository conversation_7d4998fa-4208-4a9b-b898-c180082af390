import React, { FC, useEffect, useRef, useState, memo } from 'react';
import './index.scss';

interface ILtTabsProps {
    tabs: Array<{
        title: string;
        key: string;
    }>;
    onChange?: (key: string) => void;
    activeKey?: string;
}

interface ILtTabsContentProps {
    activeKey?: string;
    lkey?: string;
    children?: React.ReactNode;
}
const LtTabs: FC<ILtTabsProps> = memo(({ tabs = [], onChange = () => { }, activeKey, }) => {
    const lineRef = useRef<HTMLDivElement>(null)
    const titleRef = useRef<any>(null);
    useEffect(() => {
        changeLine(activeKey as string)
    })
    const changeLine = (key: string) => {
        const index = tabs.findIndex(item => item.key === key)
        const title = titleRef.current && titleRef.current.children[index];
        const line = lineRef.current;
        if (line && title) {
            line.style.width = title.offsetWidth + "px"
            line.style.left = title.offsetLeft + "px"
        }

    }
    const onChangeTab = (key: string) => {
        onChange(key);
        changeLine(key);
    }
    return (
        <div className='lt_tabs'>

            <div className="lt_tabs_title_box">
                <div ref={lineRef} className="lt_tabs_title_box_line">
                    <img src="//wos.58cdn.com.cn/cDazYxWcDHJ/picasso/49ecp87c__w96_h36.png" />
                </div>
                <div className='lt_tabs_title_box_wrapper' ref={titleRef}>
                    {
                        tabs.map(item => {
                            return (
                                <div key={item.key} onClick={() => onChangeTab(item.key)} className={activeKey === item.key ? "lt_tabs_title lt_tabs_title_active" : "lt_tabs_title"}>
                                    {item.title}
                                </div>
                            )
                        })
                    }
                </div>

            </div>
        </div>
    )
}
)
const LtTabsContent: FC<ILtTabsContentProps> = memo(({ activeKey = "0", lkey, children, ...props }) => {
    const [isFirstRender, setIsFirstRender] = useState(false);

    // 只有切换到当前页卡 才渲染一次，后续不再重新渲染
    useEffect(() => {
        if (activeKey === lkey) {
            setIsFirstRender(true)
        }
    }, [activeKey])
    return (
        <>
            {
                isFirstRender ? (<div className={activeKey === lkey ? "lt_tabs_content lt_tabs_content_active" : "lt_tabs_content"}>
                    {children}
                </div>) : null
            }
        </>

        // <div className={activeKey === lkey ? "lt_tabs_content lt_tabs_content_active" : "lt_tabs_content"}>
        //     {children}
        // </div>
    )
})

export { LtTabs, LtTabsContent };