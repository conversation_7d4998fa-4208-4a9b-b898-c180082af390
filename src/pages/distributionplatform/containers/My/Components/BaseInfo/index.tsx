/*
 * @Author: z<PERSON>wu<PERSON> <EMAIL>
 * @Date: 2024-10-25 15:20:38
 * @LastEditors: zhangwu01 <EMAIL>
 * @LastEditTime: 2024-11-18 10:45:44
 * @Description: 贷款总览 详情信息
 * 
 */
import React, { FC,memo } from 'react';
import './index.scss';

interface IColumn {
    unit: string;
    dataIndex: string;
    desc: string;
}
interface IProps {
    columns: IColumn[];
    dataSource: { [propName: string]: any }
}

const BaseInfo: FC<IProps> = ({ dataSource = {}, columns = [] }) => {
    return (
        <div className='dis_base_infomation'>
            {
                columns.map((item) => {
                    return (
                        <div className="dis_base_infomation_content" key={item.dataIndex}>
                            <p className='dis_base_infomation_content_price'>
                                <span className='dis_base_infomation_content_price_num'>{dataSource[item.dataIndex]}</span>
                                {item.unit}
                            </p>
                            <p className='dis_base_infomation_content_desc'>{item.desc}</p>
                        </div>
                    )
                })
            }
        </div>
    )
}

export default memo(BaseInfo);