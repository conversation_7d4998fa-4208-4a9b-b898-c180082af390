import React, { useEffect } from "react";
import request from "api/request";
import { API } from "api/constants";
import { bridge } from '@sqb/utility';
import './index.scss';

const NewsInfo = (props) => {
    useEffect(() => {
        bridge.setTitle('签约申请');
        skipOrnot();
    }, []);

    const skipOrnot = () => {
        request.get(API.getSalesInfo).then((res) => {
            const { rCode, data } = res || {};
            if (rCode !== 0) return;
            
            if (rCode === 0) {
                if (data.salesStatus === "SIGNED") {

                    props.history.replace({ pathname: `/home` })

                } else if (data.salesStatus === "SIGNED FAIL") {

                    props.history.replace({ pathname: `/unsigned` })
                }
            } else {
                return
            }

        });
    };
    return (
        <div className="sign-home">
            <div className='sign-bottom'>
                <div className='sign-bottom-all'>
                    <div className='sign-bottom-top'></div>
                    <div className='sign-bottom-center'>审核中</div>
                    <div className='sign-bottom-footer'>我们将在3个工作日内完成审核登记，请耐心等待</div>
                </div>

            </div>
        </div>
    );
}
export default NewsInfo;
