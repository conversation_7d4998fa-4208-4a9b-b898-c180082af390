@import "~scss/index";
.container {
    width: 100vw;
    height: 100vh;
    background: linear-gradient(to bottom left, #f4fff6, #ffffff);
}

.nameImage {
    // height: 119.5px;
    height: 205px;
    // margin-top: 47px;
    margin-left: 12px;
    margin-right: 12px;
}
.phoneInput {
    border: none;
    outline: none;
    border-bottom: 0.5px solid #eaeaea;
    background-color: transparent; /* 设置背景为透明 */
    margin-left: 25px;
    margin-right: 42px;
    height: 42.5px;
    // margin-top: 46px;
    font-size: 16px;
    line-height: 22.5px;
    padding-left: 7px;
}
.phoneInput .am-input-control input::-webkit-input-placeholder {
    color: #666666;
}
.code-container-verify-img {
    width: 130px;
    height: 53px;
}
.code-container {
    display: flex;
    align-items: center;
    margin-left: 25px;
    margin-right: 42px;
    border-bottom: 0.5px solid #eaeaea;
    padding: 10px;
}

.code-input {
    flex: 1;
    border: none;
    outline: none;

    background-color: transparent; /* 设置背景为透明 */

    font-size: 16px;
    line-height: 22.5px;
    padding-left: 7px;
}
.code-input .adm-input-element::placeholder {
    color: #666666;
}

.code-button {
    border: none !important;
    padding: 5px 10px !important;
    margin-left: 10px !important;
    cursor: pointer !important;
    font-size: 13px !important ;
    line-height: 18.5px !important;
    background-color: transparent !important; /* 设置背景为透明 */
    color: #00c682 !important;
}
.login-div {
    margin-top: 40px;
    height: 48px;
    border-radius: 36px;
    background-color: #00c682;
    font-size: 17px;
    color: #ffffff;
    margin-left: 37px;
    margin-right: 37px;
    text-align: center;
    line-height: 48px;
}

.agreement-div {
    display: flex;
    margin-top: 32px;
    // align-items: center;
    align-items: flex-start; 

    .agreement-div-checkbox {
        margin-left: 55px;
        width: 14px;
        height: 14px;
        margin-top: -4px;
    }
    .adm-checkbox {
        display: flex;
        flex-direction: row-reverse;
        height: 100%;
        justify-content: space-between;
        align-items: center;
        .adm-checkbox-content {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            font-weight: 400;
            color: rgba(51, 51, 51, 1);
            padding-left: 16px;
        }
    }
    .am-checkbox-label::before {
        content: "";
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        cursor: pointer;
    }
    .agreement-box{
        padding-left: 11px;
        margin-right: 37px;
        .agreement-text {
            display: inline;
            // margin-left: 11px;
            font-size: 12px;
            color: #999999;
            order: -1;
            flex: 1;
        }
        .agreement-textName {
       
            display: inline;
            font-size: 12px;
            color: #686868;
            flex: 1;
          
        }
        
    }
   
}
.tepImage {
    position: absolute;
    height: 143px;
    // bottom: 20px;
    margin-top: 40px;
    left: 50%;
    transform: translateX(-50%);
}
