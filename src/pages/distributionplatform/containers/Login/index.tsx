import React, { useEffect, useRef, useState } from "react";
import { useLocation } from "react-router";
import { Toast, InputItem } from "antd-mobile";
import { Form, Input, Button, Checkbox } from "antd-mobile-v5";
import "./index.scss";
import { bridge } from "@sqb/utility";
import ProtocolDialog from "../../components/ProtocolDialog";
import {
    getLoginUserLearning,
    getNationalTrustLoginUserLearning,
    IProtocolItem
} from "../../server/home";
import { reportPoint } from "tools/reportPoint";
import { wmdaDataHome } from "./constants";

import { passportDistributionplatformInit } from 'api/passportUtil';

const Login = () => {
    const location = useLocation();
    const [form] = Form.useForm();
    const [codeMsg, setCodeMsg] = useState("获取验证码");
    const [path, setPath] = useState("");
    const [channel, setChannel] = useState("");
    const [topImageData] = useState({
        default:
            "//fhlui1001.58wos.com.cn/cDazYxWcDHJ/picasso/0l5j022h__w1404_h822.png",
        nationalTrust:
            "//fhlui1001.58wos.com.cn/cDazYxWcDHJ/picasso/q11e0cmh__w1404_h822.png",
    });
    const [bottomImageData] = useState({
        default:
            "//fhlui1001.58wos.com.cn/cDazYxWcDHJ/picasso/i391aale__w1372_h572.png",
        nationalTrust: "-",
    });

    const [phoneNumber, setPhoneNumber] = useState("");
    const phoneNumberRef: any = useRef();
    phoneNumberRef.current = phoneNumber;

    const [codeNumber, setCodeNumber] = useState("");
    const [codeButtonDisabled, setCodeButtonDisabled] = useState(true);
    const [loginButtonDisabled, setLoginButtonDisabled] = useState(true);
    const [agreementChecked, setAgreementChecked] = useState(false);
    // const [userLearning, setUserLearning] = useState<any>({});
    const [protocolListEMS, setProtocolListEMS] = useState<IProtocolItem[]>([]);
    const [visible, setVisible] = useState<any>(false);
    const [productDataList, setProductDataList] = useState<any>([]);
    const [title, setTitle] = useState<any>(null);

    const [codeIsClick, setCodeIsClick] = useState(false); //记录进入页面后 是否点击过 获取验证码，点击过才可以点击登录按钮

    // 图形验证码url
    const [vcodeimgurl, setVcodeimgurl] = useState("");
    // 图形验证码
    const [vcode, setVcode] = useState("");

    useEffect(() => {
        bridge.setTitle("登录");
    }, []);
    useEffect(() => {
        if (phoneNumber.length === 11) {
            setCodeButtonDisabled(false);
        } else {
            setCodeButtonDisabled(true);
        }
    }, [phoneNumber]);
    useEffect(() => {
        if (
            codeIsClick &&
            phoneNumber.length === 11 &&
            codeNumber.length >= 4
        ) {
            setLoginButtonDisabled(false);
        } else {
            setLoginButtonDisabled(true);
        }
    }, [phoneNumber, codeNumber, codeIsClick]);

    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const paramPath = searchParams.get("path") || "";
        setPath(paramPath);
        const paramChannel = searchParams.get("channel") || "default";
        setChannel(paramChannel);
        console.log("path", paramPath);
        reportPoint(wmdaDataHome.loginPagePV);

    }, []);
    useEffect(() => {
        console.log("channel===2222", channel);
        getUserLearning();
    }, [channel]);
    const getUserLearning = async () => {
        // 获取用户须知协议 数据从EMS获取
        let list;
        console.log("channel====", channel);
        if (channel === "nationalTrust") {
            list = await getNationalTrustLoginUserLearning();
        } else if (channel === "default") {
            //default
            list = await getLoginUserLearning();
        }
        console.log("list", list);
        // setUserLearning(list);
        setProtocolListEMS(list || []);
    };

    const readUserLearning = () => {
        setVisible(true);
    };
    //获取验证码
    const getCode = async () => {
        setCodeIsClick(true);
        setCodeButtonDisabled(true);
        let seconds = 60;
        setCodeMsg(seconds + "秒后重发");
        const countdownInterval = setInterval(() => {
            seconds--;
            setCodeMsg(seconds + "秒后重发");

            if (seconds <= 0) {
                if (phoneNumberRef.current.length === 11) {
                    setCodeButtonDisabled(false);
                } else {
                    setCodeButtonDisabled(true);
                }
                setCodeMsg("重新获取");
                clearInterval(countdownInterval);
            }
        }, 1000);
        m_getVerificationCode(countdownInterval);
    };
    const m_getVerificationCode = (countdownInterval) => {
        window.sdk_m_getVerificationCode &&
            window.sdk_m_getVerificationCode(
                {
                    phoneNum: phoneNumber,
                    validcode: vcode,
                    codeCallBack() {
                        m_getVerificationCode(countdownInterval);
                    },
                },
                (result) => {
                    console.log(result);
                    const { code, vcodeimgurl = "" } = result;
                    if (code === 0) {
                    } else {
                        if (`${code}` === "785") {
                            setCodeMsg("重新获取");
                            clearInterval(countdownInterval);
                            setCodeButtonDisabled(false);
                            setVcodeimgurl(vcodeimgurl);

                            //785 出图片验证码
                        } else if (`${code}` === "786") {
                            // 786  图片验证码错误（业务方需要更新图片验证码）
                            setCodeMsg("重新获取");
                            clearInterval(countdownInterval);
                            setCodeButtonDisabled(false);
                            refreshVcodeImgSrc();
                        }
                        Toast.info(result.msg || "获取验证码失败", 1);
                    }
                }
            );
    };
    const refreshVcodeImgSrc = () => {
        const imgSrc = vcodeimgurl;
        const newImgSrc =
            imgSrc.substr(0, vcodeimgurl.indexOf("time")) +
            "time=" +
            new Date().getTime();
        setVcodeimgurl(newImgSrc);
    };

    //登录
    const loginClick = async () => {
        if (loginButtonDisabled) {
            return;
        }
        if (!agreementChecked) {
            Toast.info("请阅读并勾选协议");
            return;
        }
        reportPoint(wmdaDataHome.getCodeBtnClick)
        // sdk调用登录，需要在58域下种cookie，
        // 目前只能设置isredirect=true实现，但这样登录成功后直接跳转到了inti时的path
        // 目前只能多次初始化来实现
        passportDistributionplatformInit(path);

        window.sdk_m_phoneNumLogin &&
            window.sdk_m_phoneNumLogin(
                { phoneNum: phoneNumber, verificationCode: codeNumber },
                (result) => {
                    console.log(result);
                    const { code, msg } = result;
                    if (code === 0) {
                        Toast.loading("登录中...");

                        sessionStorage.setItem('redirectPath', path);
                        //等passport把PPU塞到浏览器再执行，不然可能没有登录态还是在登录页
                        setTimeout(() => {
                            if (path) {
                                console.log("path", path);
                                window.location.replace(decodeURIComponent(path));
                            } else {
                                window.location.href = `${window.location.protocol}//${window.location.host}/agentapp/distributionplatform/index`;
                            }
                        }, 1000);

                    } else {

                        Toast.info(msg || "登录失败");
                    }
                }
            );
    };

    const handlePhoneChange = (value) => {
        // 只允许输入数字
        const newValue = value.replace(/\D/g, ""); // 只保留数字
        setPhoneNumber(newValue);
    };
    const handleCodeChange = (value) => {
        // 只允许输入数字
        const newValue = value.replace(/\D/g, ""); // 只保留数字
        setCodeNumber(newValue);
    };

    const handleVerifyCodeChange = (value) => {
        // 只允许输入数字
        // const newValue = value.replace(/\s/g, ""); // 只保留数字
        setVcode(value);
    };
    const agreementOnChange = (value) => {
        setAgreementChecked(value);
        console.log(value, "agreementOnChange");
    };
    const showProtocol = (title, protocolList) => {
        setTitle(title);
        setProductDataList(protocolList);
        setVisible(true);
    };
    return (
        <>
            <div className="container">
                <Form form={form} layout="horizontal">
                    <img
                        className="nameImage"
                        src={topImageData[channel]}
                        alt=""
                    />
                    <Form.Item name="phone">
                        <InputItem
                            className="phoneInput"
                            type="phone"
                            placeholder="请输入手机号"
                            value={phoneNumber}
                            onChange={handlePhoneChange}
                        />
                    </Form.Item>
                    <Form.Item name="code">
                        <div className="code-container">
                            <Input
                                type="tel"
                                className="code-input"
                                placeholder="请输入验证码"
                                value={codeNumber}
                                maxLength={6}
                                onChange={handleCodeChange}
                                enterKeyHint={"done"}
                            ></Input>
                            <Button
                                className="code-button"
                                onClick={() => {
                                    if (!codeButtonDisabled) {
                                        reportPoint(wmdaDataHome.getCodeBtnClick)
                                    }
                                    getCode();
                                }}
                                disabled={codeButtonDisabled}
                            >
                                {codeMsg}
                            </Button>
                        </div>
                    </Form.Item>
                    {/*    */}
                    {vcodeimgurl && (
                        <Form.Item name="vcode">
                            <div className="code-container">
                                <Input
                                    type="text"
                                    className="code-input"
                                    placeholder="请输入图形验证码"
                                    value={vcode}
                                    maxLength={6}
                                    onChange={handleVerifyCodeChange} enterKeyHint={undefined}                                ></Input>

                                <img
                                    onClick={refreshVcodeImgSrc}
                                    className="code-container-verify-img"
                                    src={vcodeimgurl}
                                />
                            </div>
                        </Form.Item>
                    )}

                    <div
                        style={{
                            // color: loginButtonDisabled ? "#e0c8d0" : "#ffffff",
                            opacity: loginButtonDisabled ? 0.5 : 1,
                        }}
                        className="login-div"
                        onClick={() => {
                            loginClick();
                        }}
                    >
                        登录
                    </div>

                    <div className="agreement-div">
                        <label className="am-checkbox-label">
                            <Checkbox
                                className="agreement-div-checkbox"
                                onChange={(checked) => {
                                    agreementOnChange(checked);
                                }}
                                icon={(checked) =>
                                    checked ? (
                                        <img
                                            className="checkImage"
                                            src="//fhlui1001.58wos.com.cn/cDazYxWcDHJ/picasso/lkb6sio4__w56_h56.png"
                                            alt=""
                                        />
                                    ) : (
                                        <img
                                            className="checkImage"
                                            src="//fhlui1001.58wos.com.cn/cDazYxWcDHJ/picasso/cmhrmacq__w56_h56.png"
                                            alt=""
                                        />
                                    )
                                }
                            ></Checkbox>
                        </label>

                        <div className="agreement-box">
                            <div className="agreement-text">我已阅读并同意</div>
                            <div
                                onClick={() => {
                                    readUserLearning();
                                }}
                                className="agreement-textName"
                            >
                                {/* 《用户须知》 */}
                                {protocolListEMS &&
                                    !!protocolListEMS.length &&
                                    protocolListEMS.map(
                                        ({
                                            isUsing,
                                            key,
                                            protocolList,
                                            name,
                                        }) =>
                                            isUsing && (
                                                <span
                                                    className="protocol-name"
                                                    key={key}
                                                    onClick={() =>
                                                        showProtocol(
                                                            name,
                                                            protocolList
                                                        )
                                                    }
                                                >
                                                    《{name}》
                                                </span>
                                            )
                                    )}
                            </div>
                        </div>

                    </div>
                    {
                        //适配safari，直接为“-” ，safari会显示一个展位图
                        bottomImageData[channel] === "-" ? null :
                            <img
                                className="tepImage"
                                src={bottomImageData[channel]}
                                alt=""
                            />
                    }

                </Form>
            </div>
            <ProtocolDialog
                protocolList={productDataList}
                visible={visible}
                setVisible={setVisible}
                title={title}
            />
        </>
    );
};
export default Login;
