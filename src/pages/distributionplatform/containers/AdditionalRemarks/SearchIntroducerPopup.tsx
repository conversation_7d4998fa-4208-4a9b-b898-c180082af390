import React, { useState, useEffect, useRef, FC } from 'react';
import { Popup } from 'antd-mobile-v5';
import request from 'api/request';
import { API } from 'api/constants';
import { Toast } from 'antd-mobile-v5';
import './SearchIntroducerPopup.scss';

interface IIntroducer {
  id: string;
  name: string;
  mobile: string;
}

interface IProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (introducer: IIntroducer) => void;
}

const SearchIntroducerPopup: FC<IProps> = ({ visible, onClose, onSelect }) => {
  const [keyword, setKeyword] = useState<string>('');
  const [searchResults, setSearchResults] = useState<IIntroducer[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 清除搜索结果
  const clearResults = () => {
    setSearchResults([]);
  };

  // 清除搜索关键词
  const clearKeyword = () => {
    setKeyword('');
    clearResults();
  };

  // 搜索介绍人
  const searchIntroducer = async (value: string) => {
    if (!value.trim()) {
      clearResults();
      return;
    }

    setLoading(true);
    try {
      const res = await request.get(API.searchIntroducer, { keyword: value });
      const { rCode, data, rMsg } = res || {};
      
      if (rCode === 0 && data) {
        setSearchResults(data.list || []);
      } else {
        Toast.show(rMsg || '搜索失败，请稍后重试');
        setSearchResults([]);
      }
    } catch (error) {
      console.error('搜索介绍人失败:', error);
      Toast.show('搜索失败，请稍后重试');
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyword(value);

    // 防抖处理
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    timerRef.current = setTimeout(() => {
      searchIntroducer(value);
    }, 300);
  };

  // 选择介绍人
  const handleSelectIntroducer = (introducer: IIntroducer) => {
    onSelect(introducer);
    onClose();
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // 弹窗关闭时清除搜索状态
  useEffect(() => {
    if (!visible) {
      clearKeyword();
    }
  }, [visible]);

  return (
    <Popup
      visible={visible}
      onMaskClick={onClose}
      onClose={onClose}
      bodyClassName="search-introducer-popup"
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        maxHeight: '70vh',
      }}
    >
      <div className="search-introducer-header">
        <div className="search-introducer-title">搜索介绍人</div>
        <div className="search-introducer-close" onClick={onClose}></div>
      </div>
      <div className="search-introducer-content">
        <div className="search-introducer-input-wrapper">
          <input
            type="text"
            className="search-introducer-input"
            placeholder="请输入姓名或手机号搜索"
            value={keyword}
            onChange={handleInputChange}
          />
          {keyword && (
            <span className="search-introducer-clear" onClick={clearKeyword}></span>
          )}
        </div>
        <div className="search-introducer-results">
          {loading ? (
            <div className="search-introducer-loading">搜索中...</div>
          ) : searchResults.length > 0 ? (
            <ul className="search-introducer-list">
              {searchResults.map((item) => (
                <li
                  key={item.id}
                  className="search-introducer-item"
                  onClick={() => handleSelectIntroducer(item)}
                >
                  <div className="search-introducer-name">{item.name}</div>
                  <div className="search-introducer-mobile">{item.mobile}</div>
                </li>
              ))}
            </ul>
          ) : keyword ? (
            <div className="search-introducer-empty">未找到相关介绍人</div>
          ) : null}
        </div>
      </div>
    </Popup>
  );
};

export default SearchIntroducerPopup;
