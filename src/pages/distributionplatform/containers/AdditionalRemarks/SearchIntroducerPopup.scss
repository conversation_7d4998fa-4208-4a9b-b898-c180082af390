@import "~scss/mixins/px2rem";

.search-introducer-popup {
  background-color: #fff;
  padding-bottom: rem(30);
  
  .search-introducer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: rem(30) rem(24);
    border-bottom: 1px solid #f5f5f5;
    
    .search-introducer-title {
      font-size: rem(32);
      font-weight: 500;
      color: #333;
    }
    
    .search-introducer-close {
      width: rem(40);
      height: rem(40);
      background: url(//j1.58cdn.com.cn/jinrong/images/ems1687853594098c2f0a2857791.png) no-repeat center;
      background-size: contain;
    }
  }
  
  .search-introducer-content {
    padding: rem(24);
    
    .search-introducer-input-wrapper {
      position: relative;
      margin-bottom: rem(24);
      
      .search-introducer-input {
        width: 100%;
        height: rem(80);
        padding: 0 rem(80) 0 rem(24);
        background-color: #f9f9f9;
        border-radius: rem(40);
        border: none;
        outline: none;
        font-size: rem(28);
        color: #333;
        
        &::placeholder {
          color: #999;
        }
      }
      
      .search-introducer-clear {
        position: absolute;
        right: rem(24);
        top: 50%;
        transform: translateY(-50%);
        width: rem(32);
        height: rem(32);
        background: url(//j1.58cdn.com.cn/jinrong/images/ems1687853594098c2f0a2857791.png) no-repeat center;
        background-size: contain;
      }
    }
    
    .search-introducer-results {
      max-height: calc(70vh - rem(200));
      overflow-y: auto;
      
      .search-introducer-loading,
      .search-introducer-empty {
        text-align: center;
        padding: rem(40) 0;
        color: #999;
        font-size: rem(28);
      }
      
      .search-introducer-list {
        list-style: none;
        margin: 0;
        padding: 0;
        
        .search-introducer-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: rem(24) rem(16);
          border-bottom: 1px solid #f5f5f5;
          
          &:active {
            background-color: #f9f9f9;
          }
          
          .search-introducer-name {
            font-size: rem(28);
            color: #333;
            font-weight: 500;
          }
          
          .search-introducer-mobile {
            font-size: rem(24);
            color: #999;
          }
        }
      }
    }
  }
}
