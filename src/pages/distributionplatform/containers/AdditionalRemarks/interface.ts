export interface IAddOrUpdateParams {
    "creditOrderId": number;
    "id": number;
    "userName": string;
    "productName": string;
    "workProvinceCode": string;
    "workCityCode": string;
    "workAreaCode": string;
    "workAddress": string;
    "homeProvinceCode": string;
    "homeCityCode": string;
    "homeAreaCode": string;
    "homeAddress": string;
    "supplyInfo": string;
    "imgUrl": string[];
}

export interface IImgItem {
    "imgUrl": (string | undefined)[];
    "imgType": number;
}

export interface IAddOrUpdateInfo {
    "creditOrderId": string;
    "id": string;
    "userName": string;
    "productName": string;
    "workProvinceCode": string;
    "workProvinceName": string;
    "workCityCode": string;
    "workCityName": string;
    "workAreaCode": string;
    "workAreaName": string;
    "workAddress": string;
    "homeProvinceCode": string;
    "homeProvinceName": string;
    "homeCityCode": string;
    "homeCityName": string;
    "homeAreaCode": string;
    "homeAreaName": string;
    "homeAddress": string;
    "supplyInfo": string;
    "gainClientSource": string;
    "supplyRemind": string;
    "imgInfo": IImgItem[];
}

export interface IAreaData {
    "initial": string;
    "name": string;
    "code": string;
    "children": IAreaData[];
}

