import request from "src/api/request";
import { Toast } from "antd-mobile-v5";
import { API } from "api/constants";
import type { IAddOrUpdateParams, IAddOrUpdateInfo, IAreaData } from "./interface";



type IMethodsType = "get" | "post" | "jsonp";
const commonRequestHandle = async (url = "", type: IMethodsType = "get", params: ICO = {}, headers: ICO = {}, otherOptions: ICO = {}) => {
    const { isEncrypted, isDecrypted } = otherOptions;
    try {
        const res = await request[type](url, params, headers, isEncrypted, isDecrypted);
        const { rMsg = "", rCode, data } = res;
        if (`${rCode}` !== "0") {
            if (rMsg) {
                Toast.show(rMsg || "系统错误");
            }
            return false;
        }
        return data || true
    } catch (error) {
        Toast.show("系统错误");
        return false;

    }
}
// 新增修改补充说明
export const addAndUpdateSupplyExplain = (params: IAddOrUpdateParams): Promise<IAddOrUpdateParams> => {
    return commonRequestHandle(API.getAddOrUpdateExplain, "post", params)
}
// 获取补充说明信息
export const getSupplyExplain = (params: { id: string }): Promise<IAddOrUpdateInfo> => {
    return commonRequestHandle(API.getSupplyExplain, "get", params)
}
// 获取地址
export const getAllAdminRegions = (): Promise<IAreaData[]> => {
    return commonRequestHandle(API.getAllAdminRegions, "get", {});
}