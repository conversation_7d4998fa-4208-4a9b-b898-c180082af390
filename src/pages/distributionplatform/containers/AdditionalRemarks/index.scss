@import "~scss/mixins/px2rem";

.additonal_remarks_box {
    height: 100vh;
    padding: rem(24);
    background-color: #F7F7F7;
    padding-bottom: rem(170);
    box-sizing: border-box;
    overflow-y: auto;
    
    .additonal_remarks_tip {
        background: linear-gradient(90deg, rgba(255, 88, 0, 0.06) 0%,rgba(255, 88, 0, 0) 100%);
        border-radius: rem(16);
        font-family: PingFangSC-Medium;
        font-size: rem(28);
        font-weight: 500;
        line-height: rem(40);
        color: #333;
        padding: rem(24);
    }

    .additonal_remarks_tip_title {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        line-height: rem(40);
        display: flex;
        align-items: center;
        margin-bottom: rem(16);

        &::before {
            content: '!';
            display: block;
            width: rem(32);
            height: rem(32);
            border-radius: 50%;
            background: #ff6b3f;
            margin-right: rem(16);
            font-size: rem(24);
            line-height: rem(32);
            color: #fff;
            text-align: center;
        }
    }

    .additonal_remarks_box_base_upload {
        margin: rem(48) 0 0;
    }

    .additonal_remarks_box_base_upload_title {
        font-family: PingFangSC-Semibold;
        font-size: rem(32);
        font-weight: 700;
        line-height: rem(45);
        color: #333;
    }

    .additonal_remarks_box_base_upload_tip {
        width: 100%;
        margin-top: rem(24);
        font-family: PingFangSC-Regular;
        font-size: rem(28);
        font-weight: 400;
        width: 100%;
        padding: rem(16);
        box-sizing: border-box;
        background: linear-gradient(90deg, rgba(255, 88, 0, 0.06) 0%, rgba(255, 88, 0, 0) 100%);
        border-radius: rem(8);
        display: flex;

        span {
            flex: 1 0 auto;
        }
    }

    .form_container {
        .form_container_box {
            padding: 0 rem(8);
        }

        .adm-list-card {
            margin: rem(24) 0 0;
        }
        
        .adm-form-item-label {
            font-family: PingFangSC-Regular;
            font-weight: 400;
            color: #333;
        }

        .right {
            border-bottom: 1px solid rgba(242, 242, 242, 1);
        }

        .adm-list-item-content {
            padding-right: 0;
        }

        input, textarea {
            height: rem(40);
            line-height: rem(40);

            &::placeholder {
                font-family: PingFangSC-Regular;
                font-weight: 400;
                color: #999999;
            }
        }
    }

    .upload_image_container_item {
        padding: 0;
    }

    .upload_image_container_item_title_box {
        font-family: PingFangSC-Regular;
        font-size: rem(28);
        font-weight: 400;
        line-height: rem(40);
        color: #333;
    }
    
    .bottom_button {
        left: 0;
        right: 0;
        padding-left: rem(24);
        padding-right: rem(24);
    }
}
