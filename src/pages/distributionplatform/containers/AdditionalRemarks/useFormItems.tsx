
import { IForm } from "commons/MyForm";

import { FormInstance } from 'antd-mobile-v5/es/components/form';
import { PickerColumn } from "antd-mobile-v5/es/components/picker-view";
const columns = [
    {
        label: "以及",
        value: "1",
        children: [
            {
                label: "耳机",
                value: "2",
                children: [
                    {
                        label: "三级",
                        value: "1",
                    }
                ]
            }
        ]
    },

]
const relation = [
    {label: "陌拜", value: "陌拜", key: "1"},
    {label: "自己亲戚&亲属", value: "自己亲戚&亲属", key: "2"},
    {label: "自己同学&同事", value: "自己同学&同事", key: "3"},
    {label: "以前老客户", value: "以前老客户", key: "4"},
    {label: "客户转介-亲属&亲戚", value: "客户转介-亲属&亲戚", key: "5"},
    {label: "客户转介-同学&同事", value: "客户转介-同学&同事", key: "6"},
    {label: "客户转介-股东及合作伙伴", value: "客户转介-股东及合作伙伴", key: "7"},
    {label: "客户转介-上下游服务商", value: "客户转介-上下游服务商", key: "8"},
    {label: "客户转介-同市场同行", value: "客户转介-同市场同行", key: "9"},

]
export default function useFormItems(
    areaEnum: PickerColumn = [],

) {

    const formArr: IForm = [
        {
            title: "用户信息",
            fields: [
                {
                    type: "input",
                    label: "用户",
                    name: "userName",
                    disabled: true
                },
                {
                    type: "input",
                    label: "产品",
                    name: "productName",
                    disabled: true
                },
                {
                    type: "cascader",
                    label: "常用地址",
                    name: "workLocation",
                    column: areaEnum,
                    labelValue: true,
                    rules: [{
                        validator: (_: any, value) => {
                            if (!value || value.length !== 3) {
                                return Promise.reject(new Error("常用地址"))
                            }
                            return Promise.resolve("")

                        }
                    }
                    ]
                },
                {
                    type: "input",
                    label: "(常用)详细地址",
                    name: "workAddress",
                    rules: [{ required: true, message: "（工作）详细地址必填" }],

                },
                // {
                //     type: "cascader",
                //     label: "家庭地址",
                //     name: "homeLocation",
                //     column: areaEnum,
                //     labelValue: true,
                //     rules: [{
                //         validator: (_: any, value) => {
                //             if (!value || value.length !== 3) {
                //                 return Promise.reject(new Error("家庭地址必填"))
                //             }
                //             return Promise.resolve("")

                //         }
                //     }
                //     ]
                // },
                // {
                //     type: "input",
                //     label: "(家庭)详细地址",
                //     name: "homeAddress",
                //     rules: [{ required: true, message: "（工作）详细地址必填" }],

                // },
                {
                    type: "picker",
                    label: "获客来源",
                    name: "gainClientSource",
                    column: relation,
                    maxLength: 100,
                    rules: [{ required: true, message: "获客来源必填且不能超过100字" }],
                },
                {
                    type: "textarea",
                    label: "补充说明",
                    name: "supplyInfo",
                    maxLength: 100,
                    rules: [{ required: true, message: "补充说明必填且不能超过100字" }],
                },
            ]
        },

    ]
    return formArr

}
