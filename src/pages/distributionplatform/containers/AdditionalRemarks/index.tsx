import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { FormInstance } from 'antd-mobile-v5/es/components/form';
import { PickerColumn } from 'antd-mobile-v5/es/components/picker-view';
import { Toast } from "antd-mobile-v5";

import MyForm from "commons/MyForm";
import { bridge } from "@sqb/utility";
import UploadImageContainer, { IImageRef, IImageItem } from 'commons/UploadImageContainer';
import useFormItems from './useFormItems';
import BottomButton from 'commons/BottomButton';
import { getSupplyExplain, addAndUpdateSupplyExplain, getAllAdminRegions } from "./common";
import type { IAreaData, IAddOrUpdateInfo, IImgItem } from './interface';
import { removeQueryParameter } from 'src/tools/utils';
import "./index.scss";

const UploadPicListInit: IImageItem[] = [
    {
        title: "与客户合影",
        formKey: 1,
        isRequired: true,
        maxCount: 3,
    },
    {
        title: "营业执照",
        formKey: 2,
        maxCount: 3,
    },
    {
        title: "固定资产证明",
        formKey: 3,
        maxCount: 5,
    },
    {
        title: "收款流水及应收账款类",
        formKey: 4,
        maxCount: 10,
    },
    {
        title: "项目合同类",
        formKey: 5,
        maxCount: 5,
    },
];

interface IPicDataSource {
    [propName: string]: {
        url: string
    }[]
}

// 存储表单数据和上传数据的key
const STORAGE_KEY_PREFIX = 'additionalRemarks_';

// 保存表单数据和上传数据到localStorage
const saveFormData = (id: string, formValues: any, uploadData: IPicDataSource) => {
    if (!id) return;

    try {
        const storageKey = `${STORAGE_KEY_PREFIX}${id}`;
        const dataToSave = {
            formValues,
            uploadData
        };
        localStorage.setItem(storageKey, JSON.stringify(dataToSave));
    } catch (error) {
        console.error('Error saving form data:', error);
    }
};

// 从localStorage获取保存的表单数据和上传数据
const getStoredFormData = (id: string) => {
    if (!id) return null;

    try {
        const storageKey = `${STORAGE_KEY_PREFIX}${id}`;
        const storedData = localStorage.getItem(storageKey);

        if (storedData) {
            return JSON.parse(storedData);
        }
    } catch (error) {
        console.error('Error retrieving form data:', error);
    }

    return null;
};

const clearStorage = (id: string) => {
    console.log(id, "clearStorage");
    if (!id) return;

    try {
        const storageKey = `${STORAGE_KEY_PREFIX}${id}`;
        localStorage.removeItem(storageKey);
    } catch (error) {
        console.error('Error clearing stored data:', error);
    }
}

function AdditionalRemarks(props: any) {
    const id = useParams<{ id: string }>().id || props.id;
    const formRef = useRef<FormInstance>();
    const imgRef = useRef<IImageRef>(null);
    // loading
    const [loading, setLoading] = useState(true);
    // 地址信息
    const [areaEnum, setAreaEnum] = useState<PickerColumn>([]);
    // 回显数据
    const [supplyExplainInfo, setSupplyExplainInfo] = useState<IAddOrUpdateInfo>({} as any);
    // 上传影像
    const [uploadPicList, setUploadPicList] = useState<IImageItem[]>([]);
    // 上传影像dataSource
    const [uploadDataSource, setUploadDataSource] = useState<IPicDataSource>({});
    // 历史影像
    const [historyPicList, setHistoryPicList] = useState<IImageItem[]>([]);
    // 历史影像dataSource
    const [historyDataSource, setHistoryDataSource] = useState<IPicDataSource>({});
    const formArr = useFormItems(areaEnum);
    const [shouldSaveOnUnmount, setShouldSaveOnUnmount] = useState(true);

    function formatSelectionData(data: IAreaData[]) {
        if (data && data.length > 0) {
            return data.map((item) => {
                return {
                    label: item.name,
                    value: item.code,
                    children: formatSelectionData(item.children)
                }
            })
        }
    }

    const clearData = () => {
        formRef.current?.resetFields();
        setUploadPicList([]);
        setUploadDataSource({});
        setHistoryPicList([]);
        setHistoryDataSource({});
        setSupplyExplainInfo({} as IAddOrUpdateInfo);
    }

    // 加载存储的表单数据和上传数据
    const loadStoredData = () => {
        if (!id) return;

        const storedData = getStoredFormData(id);
        if (storedData) {
            const { formValues, uploadData } = storedData;
            console.log('加载存储的数据', { formValues, uploadData });

            // 设置表单数据
            if (formValues) {
                console.log(formRef.current?.getFieldsValue(), "formRef.current");
                formRef.current?.setFieldsValue(formValues);
            }

            if(uploadData){
                console.log(uploadData, "uploadData");
                setUploadDataSource(uploadData);
            }
        }
    };

    // 保存表单数据和上传数据
    const saveCurrentFormData = () => {
        if (!id || !formRef.current) return;

        try {
            const formValues = formRef.current.getFieldsValue();
            const imageValues = imgRef.current?.getImageValues();
            saveFormData(id, formValues, imageValues);
            console.log('已保存表单数据和上传数据', { formValues, imageValues });
        } catch (error) {
            console.error('Error saving current form data:', error);
        }
    };

    useEffect(() => {
        // 改为了弹窗形式，不修改title
        // bridge.setTitle("上传资料");

        if (id) {
            getInit();
        } else {
            clearData();
        }

        // 组件卸载时保存数据
        return () => {
            if (shouldSaveOnUnmount){
                saveCurrentFormData();
            }
        };
    }, [id, shouldSaveOnUnmount])

    function handlePicDataSource(data: IImgItem[]) {
        if (!Array.isArray(data) || data.length <= 0) {
            return {}
        }
        const res = data.reduce((prev: any, cur) => {
            if (cur.imgUrl?.length > 0) {
                prev[cur.imgType] = cur.imgUrl.map(item => {
                    return {
                        url: item
                    }
                })
            }
            return prev;
        }, {})

        return res;
    }

    // 格式化影像数据
    const formatImgData = (data: IImgItem[] = []) => {
        const uploadPicListTemp = UploadPicListInit.map(item => {
            const itemN: any = { ...item };
            const itemHas = data.filter(d => d.imgType === itemN.formKey) || [];
            const itemImgUrlLen = (itemHas[0]?.imgUrl || []).length;

            if (itemHas.length > 0 && itemImgUrlLen > 0) {
                itemN.maxCount = itemN.maxCount >= itemImgUrlLen
                    ? itemN.maxCount - itemImgUrlLen
                    : 0;
            }
            itemN.title = `${itemN.title}（${itemN.maxCount}张）`;
            itemN.hidden = itemN.maxCount <= 0;
            if (itemImgUrlLen > 0) {
                itemN.isRequired = false;
            }

            return itemN;
        });

        const historyPicListTemp = UploadPicListInit.map(item => {
            const itemN = { ...item };
            const itemHas = data.filter(d => d.imgType === itemN.formKey) || [];
            itemN.disabled = true;
            itemN.isRequired = false;
            itemN.hidden = !itemHas[0]?.imgUrl?.length;
            return itemN;
        });

        setUploadPicList(uploadPicListTemp);
        // setUploadDataSource(handlePicDataSource(data));

        setHistoryPicList(historyPicListTemp);
        setHistoryDataSource(handlePicDataSource(data));
    }

    // 初始化数据
    const getInit = () => {
        Promise.all([getAllAdminRegions(), getSupplyExplain({ id })]).then((res) => {
            // mock
            // res=[{},{}];
            // res[1].imgInfo = [
            //     {
            //         imgType: 1,
            //         imgUrl: [
            //             "https://cdntestv1.58v5.cn/hjIEdCbAZhXIQ/bxscwos/019ac75749f2435e8c0f3ccf55c4fe2a.1732846294160.png",
            //             "https://cdntestv1.58v5.cn/hjIEdCbAZhXIQ/bxscwos/019ac75749f2435e8c0f3ccf55c4fe2a.1732846294160.png",
            //             "https://cdntestv1.58v5.cn/hjIEdCbAZhXIQ/bxscwos/019ac75749f2435e8c0f3ccf55c4fe2a.1732846294160.png",
            //         ]
            //     },
            //     {
            //         imgType: 3,
            //         imgUrl: [
            //             "https://cdntestv1.58v5.cn/hjIEdCbAZhXIQ/bxscwos/019ac75749f2435e8c0f3ccf55c4fe2a.1732846294160.png",
            //         ]
            //     },
            // ];

            setLoading(false);
            const [areaData, supplyExplainInfoData] = res;
            const result = formatSelectionData(areaData);
            setAreaEnum(result);
            setSupplyExplainInfo(supplyExplainInfoData);
            formatImgData(supplyExplainInfoData.imgInfo || []);

            const formData = {
                ...supplyExplainInfoData,
                workLocation: [
                    supplyExplainInfoData.workProvinceCode,
                    supplyExplainInfoData.workCityCode,
                    supplyExplainInfoData.workAreaCode,
                ]
            };
            formRef.current?.setFieldsValue(formData);
            loadStoredData();
        })
    }

    const validate = () => {
        const validateImagesList = imgRef.current!.validateImages();

        if (validateImagesList.length > 0) {
            Toast.show(`请上传${validateImagesList[0].title}`);
            return true;
        }
        return false;
    }

    function handleParamsPicValue(picValue: { url: string }[]) {
        const finalValue = picValue.map(item => {
            if (item.url.includes("token")) {
                return removeQueryParameter(item.url, "token");
            } else {
                return item.url;
            }
        })
        return finalValue;
    }

    const handlePicParams = (values: any) => {
        const { workLocation } = values;
        const imgList = imgRef.current!.getImageValues();
        debugger
        // 合并历史影像和当前影像
        let imgListTemp = {};
        let imgInfo: IImgItem[] = [];
        UploadPicListInit.forEach(({ formKey }) => {
            imgListTemp[formKey] = (imgList[formKey] || []).concat(historyDataSource[formKey] || []);
        })
        Object.keys(imgListTemp).forEach((item) => {
            if (imgListTemp[item].length > 0) {
                imgInfo.push({
                    imgType: (+item),
                    imgUrl: handleParamsPicValue(imgListTemp[item])
                });
            }
        })

        return {
            creditOrderId: supplyExplainInfo.creditOrderId,
            id: supplyExplainInfo.id,
            ...values,
            imgInfo,
            workProvinceCode: workLocation[0],
            workCityCode: workLocation[1],
            workAreaCode: workLocation[2],

        }
    }

    const submit = () => {
        formRef.current?.validateFields().then((values) => {
            if (validate()) {
                return;
            }

            const params = handlePicParams(values);
            console.log(params);
            setLoading(true)
            // 提交成功后，设置不需要保存缓存
            setShouldSaveOnUnmount(false);
            addAndUpdateSupplyExplain(params).then((res) => {
                if (res) {
                    clearStorage(id);
                    Toast.show('提交成功');
                    if (typeof props.onSubmitCb === 'function') {
                        props.onSubmitCb(id);
                    } else {
                        props.history.go(-1);
                    }
                }
            }).catch((err) => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
            })
        }).catch((err) => {
            console.log(err);
        })
    }

    return (
        <div className='additonal_remarks_box'>
            {supplyExplainInfo?.supplyRemind && (
                <div className="additonal_remarks_tip">
                    <p className='additonal_remarks_tip_title'>补件提醒</p>
                    {supplyExplainInfo?.supplyRemind}
                </div>
            )}
            <div className="additonal_remarks_box_base_infomation">
                <MyForm form={formRef} formColumn={formArr}></MyForm>
            </div>
            <div className="additonal_remarks_box_base_upload">
                <div className="additonal_remarks_box_base_upload_title">
                    上传影像
                    <p className='additonal_remarks_box_base_upload_tip'>
                        <span>提示：</span>
                        图片上传数量不可超该目录上限，提交后不允许删除
                    </p>
                </div>
                <UploadImageContainer
                    multiple
                    ref={imgRef}
                    imagList={uploadPicList}
                    dataSource={uploadDataSource}
                    onTogglePreviewCb={props.onTogglePreviewCb}
                />
            </div>

            {(supplyExplainInfo.imgInfo || []).length > 0 && (
                <div className="additonal_remarks_box_base_upload">
                    <div className="additonal_remarks_box_base_upload_title">
                        历史影像
                    </div>
                    <UploadImageContainer
                        multiple={true}
                        imagList={historyPicList}
                        dataSource={historyDataSource}
                        onTogglePreviewCb={props.onTogglePreviewCb}
                    />
                </div>
            )}
            <div className="fix_bottom">
                <BottomButton loading={loading} onClick={submit}>确定</BottomButton>
            </div>
        </div>
    )
}

export default AdditionalRemarks;