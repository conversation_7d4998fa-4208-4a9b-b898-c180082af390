import React, { useState, useEffect, useRef } from "react";
import { Tabs, Modal } from "antd-mobile-v5";
import { Toast } from "antd-mobile";
// import { SwiperRef } from "antd-mobile-v5/es/components/swiper";
import { bridge } from "@sqb/utility";
import { reportPoint } from "tools/reportPoint";
import { formatProtocol } from "tools/utils/index";
import request from "api/request";
import { API } from "api/constants";
import { copyFn } from "tools/utils/libs/copyFn";
import { wmdaDataHome } from "./constants";
import { getLocation, isWxMiniProgramUrl } from "tools/utils/index";


import "./index.scss";

// 海报轮询间隔时间
const PollIntervalTime = 5000;
// 海报轮询定时器
let timer: any = null;
// 海报刷新倒计时定时器
let refreshTimer: any = null;

const Home = (props) => {
    // const swiperRef = useRef<SwiperRef>(null);
    const platformhome = useRef(null);
    const [activeIndex, setActiveIndex] = useState(0);
    const [pageData, setpageData] = useState<any>({});
    const [tabData, setTabData] = useState<any>([{}]);
    const [toggleData, settoggleData] = useState<any>([]);
    const [pageNumber, setpageNumber] = useState(1);
    const [tool, setTool] = useState(0);
    // 海报展示开关
    const [placardVisible, setPlacardVisible] = useState<boolean>(false);
    // 海报所属产品id
    const [placardProductId, setPlacardProductId] = useState<string>("");
    // productLinkType 1 api 2 h5
    const [productLinkType, setProductLinkType] = useState<string>("");
    // 海报图片
    const [placard, setPlacard] = useState<string>("");
    // 海报刷新剩余时间
    const [placardRefreshTime, setPlacardRefreshTime] = useState<number>(0);
    // 海报刷新按钮点击状态
    const [placardRefreshDisable, setPlacardRefreshDisable] = useState<boolean>(false);

    const [shareLink, setShareLink] = useState("");
    // timmerRef
    const timmerRef = useRef<any>(null);
    const [copyButtonIsShow, setCopyButtonIsShow] = useState(true);

    const placardVisibleRef = useRef<any>(null);
    placardVisibleRef.current = placardVisible;

    const placardRefreshTimeRef = useRef<any>(null);
    placardRefreshTimeRef.current = placardRefreshTime;

    useEffect(() => {
        bridge.setTitle("产品推荐");
        getData();
    }, []);
    useEffect(() => {
        getTabData(1, false);
    }, [activeIndex]);

    const getData = () => {
        request.get(API.getSalesInfo).then((res) => {
            const { rCode, data, rMsg } = res || {};
            // if (rCode !== 0) return;
            // const {list} = data
            if (rCode === 0) {
                setpageData(data);
            } else {
                Toast.show(rMsg || "系统错误,请稍后重试");
            }
        });
    };

    const getTabData = (dec, wins) => {
        request.get(API.queryHometab).then((res) => {
            const { rCode, data, rMsg } = res || {};
            // if (rCode !== 0) return;
            console.log(dec, "页码");
            console.log(wins, "状态");

            if (rCode === 0) {
                if (data.length > 0) {
                    setTabData(data);
                    getToogleDatas(
                        dec,
                        wins,
                        data[activeIndex]?.productCategoryCode
                    );
                } else {
                    return;
                }
            } else {
                Toast.show(rMsg || "系统错误,请稍后重试");
            }
        });
    };

    const getToogleDatas = (num, isAdd, key) => {
        const param = { pageNo: num, pageSize: 20, productCategoryCode: key };

        num === 1 && Toast.loading("请求中...");
        request.get(API.queryHometoggle, param).then((res) => {
            num === 1 && Toast.hide();
            const { rCode, data, rMsg } = res || {};
            if (rCode === 0) {
                const arr = data?.list;
                if (!isAdd) {
                    settoggleData(arr);
                } else {
                    settoggleData(toggleData && toggleData.concat(arr));
                }
                setTool(data?.total);
            } else {
                Toast.show(rMsg || "系统错误,请稍后重试");
            }
        });
    };

    // 复制链接
    const copyLink = async (productId, productLinkUrl, productLinkType, event) => {
        event.stopPropagation();

        // 如配置的是微信小程序的链接，直接复制链接
        if (isWxMiniProgramUrl(productLinkUrl)) {
            setShareLink(productLinkUrl);
        } else {
            const param = { productId, productLinkType };
            const res = await request.get(API.queryHomecopy, param);
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) {
                Toast.show(rMsg || "系统错误,请稍后重试");
                return;
            }
            setShareLink(data?.productLink);
        }

        setTimeout(() => {
            copyFn("share-popup-android-content", "复制成功 ");
        }, 10);
    };

    const sharePicture = async (productId, productLinkType, event) => {
        event.stopPropagation();
        reportPoint()
        Toast.loading("生成中...", 0);
        setPlacardProductId(productId);
        setProductLinkType(productLinkType)
        if (tabData[activeIndex]?.productCategoryCode === 1) {
            const loaction = await getLocation();
            console.log(loaction, "---loaction");
            downPlacard(productId, event, loaction, productLinkType)
        } else {
            downPlacard(productId, event, {}, productLinkType)
        }
    }

    const downPlacard = async (productId, event, loaction, productLinkType) => {
        if (event) {
            event.stopPropagation();
        }
        performRequest(productId, loaction, productLinkType);

        if (tabData[activeIndex]?.productCategoryCode === 1) {
            timer = setInterval(() => {
                performRequest(productId, loaction, productLinkType, 1);
            }, PollIntervalTime);
            timmerRef.current = timer;
        }
    };

    // 只有轮询时传isPolling=1
    async function performRequest(productId, loaction, productLinkType, isPolling?) {
        let params = {
            productId,
            productCategoryCode: tabData[activeIndex]?.productCategoryCode,
            productLinkType,
            ...loaction
        };
        if (isPolling) {
            params.isPolling = isPolling;
        }
        const res = await request.get(API.queryHomePlacardUrl, params);
        // console.info(`%c res`, 'font-size:16px;color:green;', isPolling, res.data);

        setPlacardRefreshDisable(false);

        const { rCode, data, rMsg } = res || {};
        if (rCode !== 0 || !data?.posterUrl) {
            clearInterval(timmerRef.current)
            Toast.show(rMsg || "系统错误,请稍后重试");
            clearTimeInterval();
            return;
        }

        // console.info(`%c placardVisibleRef.current`, 'font-size:16px;color:red;', placardVisibleRef.current);
        if (!placardVisibleRef.current) {
            if (tabData[activeIndex]?.productCategoryCode === 1 && !timer) {
                return;
            }
            setPlacard(data.posterUrl);
            setPlacardRefreshTime(data.countdown || 0);
            setPlacardVisible(true);
            Toast.hide();
            platformhome.current.style.overflowY = "hidden";
        } else {
            if (data.isUpdate === "1") {
                setPlacard(data.posterUrl);
                setPlacardRefreshTime(data.countdown || 0);
            }
        }
    }
    useEffect(() => {
        if (placardVisible) {
            // 贷款产品轮询刷新海报
            if (tabData[activeIndex]?.productCategoryCode === 1) {
                if (placardRefreshTime <= 0) {
                    handleRefreshPlacard();
                } else {
                    if (!refreshTimer) {
                        refreshTimer = setInterval(() => {
                            let t = placardRefreshTimeRef.current;
                            t--;
                            setPlacardRefreshTime(t);
                        }, 1000);
                    }
                }
            }
        }
    }, [placardVisible, placardRefreshTime]);

    function clearTimeInterval() {
        if (timer) {
            clearInterval(timer);
            timer = null;
        }
        if (refreshTimer) {
            clearInterval(refreshTimer);
            refreshTimer = null;
        }
    }

    const handleClosePlacardModal = () => {
        clearTimeInterval();
        setPlacardVisible(false);
        setPlacardProductId("");
        setProductLinkType("")
        platformhome.current.style.overflowY = "auto";
    };

    const toSkip = (productId, productLinkUrl, productLinkType, event) => {
        if (Number(tabData[activeIndex]?.productCategoryCode) === 1) {
            //贷款直接跳H5
            // window.location.href = productLinkUrl;
            // 
            sharePicture(productId, productLinkType, event)
            return;
        }
        // 如配置微信小程序的链接,直接跳转
        if (isWxMiniProgramUrl(productLinkUrl)) {
            window.location.href = productLinkUrl;
            return;
        }

        const param = { productId, productLinkType };
        request.get(API.queryHomecopy, param).then((res) => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return;
            if (rCode === 0 && data && data.productLink) {
                window.location.href = data.productLink;
            } else {
                Toast.show(rMsg || "系统错误,请稍后重试");
            }
        });
    };

    const viewMore = () => {
        const sum = pageNumber + 1;
        setpageNumber(sum);
        getTabData(sum, true);
    };

    const handleRefreshPlacard = async () => {
        if (placardRefreshDisable) return;

        setPlacardRefreshDisable(true);
        setPlacardRefreshTime(0);
        clearTimeInterval();
        const loaction = await getLocation();
        console.log(loaction, "refresh---loaction");
        downPlacard(placardProductId, null, loaction, productLinkType);
    }

    return (
        <div className="platformhome-home" ref={platformhome}>
            <div>
                {
                    pageData?.platformHeaderUrl === null || pageData?.platformHeaderUrl === "" ? <div className="platformhome-home-top-img">
                        <div className="platformhome-home-top-img-top">
                            <span className="platformhome-home-top-58img"></span>
                            <span className="platformhome-home-top-58img-conten"></span>
                            <span className="platformhome-home-top-58img-text">
                                {" "}
                                {pageData?.channelName}{" "}
                            </span>
                        </div>
                    </div> : <img className="platformhome-home-top-img-top-disposition" src={pageData?.platformHeaderUrl} alt="" />
                }

                <div className="platformhome-bottom">
                    <div className="platformhome-bottom-tbs">
                        <Tabs
                            activeLineMode="fixed"
                            activeKey={
                                tabData &&
                                String(tabData?.[activeIndex].productCategoryCode) || ""
                            }
                            onChange={(key) => {
                                tabData.forEach(function (element, index) {
                                    if (
                                        Number(element.productCategoryCode) ===
                                        Number(key)
                                    ) {
                                        setActiveIndex(index);
                                        return index;
                                    }
                                });
                                setpageNumber(1);
                                //刷新时，pm要求情况数据交互
                                settoggleData([]);
                                // swiperRef.current?.swipeTo(index);
                            }}
                        >
                            {tabData &&
                                tabData?.map((item) => (
                                    <Tabs.Tab
                                        title={item.productCategoryName}
                                        key={item.productCategoryCode}
                                    ></Tabs.Tab>
                                ))}
                        </Tabs>
                    </div>
                    <div className="platformhome-bottom-poslvie">
                        <div className="platformhome-bottom-all-list">
                            {toggleData &&
                                toggleData?.map((item) => {
                                    return (
                                        <div
                                            className="platformhome-bottom-all"
                                            key={`product${item.productId}`}
                                            onClick={(e) =>
                                                toSkip(
                                                    item.productId,
                                                    item.productLinkUrl,
                                                    item.productLinkType,
                                                    e
                                                )
                                            }
                                        >
                                            <div
                                                className="platformhome-bottom-all-left"
                                                style={{
                                                    background: `url(${formatProtocol(
                                                        item.headImageUrl
                                                    )})`,
                                                    backgroundSize: "cover",
                                                }}
                                            ></div>
                                            <div className="platformhome-bottom-all-right">
                                                <div className="platformhome-bottom-all-right-big">
                                                    {item.productName}
                                                </div>
                                                <div className="platformhome-bottom-all-right-zhong-map">
                                                    {item.productAttributeList?.map(
                                                        (keys, index) => {

                                                            return (
                                                                <div
                                                                    className="platformhome-bottom-all-right-zhong"
                                                                    key={`product${item.productId}Attribute${index}`}
                                                                >
                                                                    <div className="platformhome-bottom-all-right-zhong-one">
                                                                        {
                                                                            keys.description
                                                                        }
                                                                    </div>{" "}
                                                                    <div className="platformhome-bottom-all-right-small-two">
                                                                        {
                                                                            keys.title
                                                                        }
                                                                    </div>
                                                                </div>
                                                            );
                                                        }
                                                    )}
                                                </div>


                                                <div className="platformhome-bottom-all-right-sur">

                                                    <div className="platformhome-bottom-all-right-sur-two">

                                                        {
                                                            item.isSharingPoster === 1 ?
                                                                <span
                                                                    className="platformhome-bottom-all-right-sur-two-b"
                                                                    onClick={(
                                                                        e
                                                                    ) => {
                                                                        console.log(item);
                                                                        reportPoint({ ...wmdaDataHome.sharePoster, productName: item.productName })
                                                                        sharePicture(item.productId, item.productLinkType, e)
                                                                    }


                                                                    }
                                                                >
                                                                    分享海报
                                                                </span> : null
                                                        }

                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                        </div>

                        {tool <= 20 ||
                            (tool === toggleData && toggleData.length) ? (
                            ""
                        ) : (
                            <div className="platformhome-bt-more">
                                <div onClick={() => viewMore()}>
                                    查看更多{" "}
                                    <span className="platformhome-bt-img"></span>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
            <div className="share-text">
                <input
                    id="share-popup-android-content"
                    type="text"
                    readOnly
                    value={shareLink}
                />
            </div>

            <Modal
                visible={placardVisible}
                className="placard-modal"
                content={
                    <div className="placard-body">
                        <img
                            className="placard-pic"
                            src={formatProtocol(placard)}
                            alt="海报"
                        />
                        <p className="modal-tip">长按保存或分享海报</p>
                        {tabData[activeIndex]?.productCategoryCode === 1 &&
                            <button
                                className="placard-refresh-btn"
                                onClick={() => handleRefreshPlacard()}
                            >
                                刷新海报
                                {placardRefreshTimeRef.current > 0 && (
                                    <span>
                                        ({placardRefreshTimeRef.current}s后自动刷新)
                                    </span>
                                )}
                            </button>
                        }
                        <i
                            className="icon-close"
                            onClick={() => handleClosePlacardModal()}
                        ></i>
                    </div>
                }
            ></Modal>
        </div>
    );
};

export default Home;
