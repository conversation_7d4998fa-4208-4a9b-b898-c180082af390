@import "~scss/index";

.platformhome-home {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 1);
    // position: absolute;
    overflow-y: auto;
    .platformhome-home-top-img {
        width: 100%;
        height: 130px;
        background: url('#{$wos-cdn}ak3a7ad5__w1500_h520.png') no-repeat;
        background-size: cover;
    }
    .platformhome-home-top-img-top-disposition{
        width: 100%;
        height: 130px;

    }
    .platformhome-home-top-img-top{
        height: 15px;
        display: flex;
        align-items: center;
        padding-top: 8px;
        padding-left: 12px;
        .platformhome-home-top-58img{
            display: inline-block;
            height: 15px;
            width: 120px;
            background: url('#{$wos-cdn}r81ufkrd__w432_h58.png') no-repeat;
            background-size: cover;
        }
        .platformhome-home-top-58img-conten{
            margin: 2px 8px 0 8px;
            display: inline-block;
            height: 8px;
            width: 1px;
            background: url('#{$wos-cdn}ftcbsef9__w4_h30.png') no-repeat;
            background-size: cover;
        }
        .platformhome-home-top-58img-text{
            font-family: PingFangSC-Medium;
            font-size: 11px;
            font-weight: 500;
            color: rgba(88, 88, 88, 1);
            margin-top: 3px;
        }
    }

    .platformhome-center {
        width: 100%;
        height: 168.5px;
        position: relative;
        // background: url("https://wos.58cdn.com.cn/cDazYxWcDHJ/picasso/157svfn8__w1500_h1112.png") no-repeat;
        // background-size: cover;
        // background-color: red;
        background-color: rgba(241, 253, 249, 1);

        .platformhome-center-bottom {
            width: 100%;
            height: 174.5px;
            position: absolute;
            bottom: 12px;

            .platformhome-center-bottom-magin {
                height: 100%;
                margin-left: 14px;
                margin-right: 14px;
                overflow: hidden;
                background: linear-gradient(180deg, rgba(230, 255, 246, 0.85) 0%, rgba(241, 255, 250, 1) 17%, rgba(255, 255, 255, 1) 100%);
                border-radius: 8px;

                .platformhome-center-bottom-top {
                    display: flex;
                    justify-content: space-around;
                    margin-top: 20px;
                    margin-left: 4px;
                    margin-right: 4px;
                }

                .platformhome-center-bottom-center {
                    width: 100%;
                    height: 0.5px;
                    margin-top: 16px;
                    overflow: hidden;

                    .platformhome-center-bottom-border {
                        height: 0.5px;
                        background-color: rgba(234, 234, 234, 1);
                        margin-left: 12px;
                        margin-right: 12px;
                    }
                }

                .platformhome-center-bottom-bottom {
                    display: flex;
                    justify-content: space-around;
                    margin-top: 20px;
                    margin-left: 4px;
                    margin-right: 4px;
                }
            }

        }


        .platformhome-title {
            width: 33%;
            height: 50.5px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .platformhome-title-num {
                height: 20px;
                font-family: DINAlternate-Bold;
                font-size: 20px;
                font-weight: 700;
                line-height: 20px;
                color: rgba(51, 51, 51, 1);
                text-align: center;
            }

            .platformhome-title-mon {
                width: 69px;
                height: 12px;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                font-weight: 400;
                line-height: 12px;
                color: rgba(102, 102, 102, 1);
                text-align: center;
                margin-top: 5px;
            }
        }


    }


    .platformhome-bottom-all {
        display: flex;
        // margin-top: 5px;
        margin-bottom: 27px;
        justify-content: center;
        padding-left: 12px;
        padding-right: 12px;
        .platformhome-bottom-all-left {
            width: 84px;
            height: 84px;
            background-size: cover !important;
            border-radius: 10px;
        }

        .platformhome-bottom-all-right {
            // width: 77%;
            height: 90px;
            display: flex;
            flex-direction: column;
            flex: 1;
            .platformhome-bottom-all-right-big {
                width: 190px;
                height: 22.5px;
                font-family: PingFangSC-Semibold;
                font-size: 16px;
                font-weight: 700;
                line-height: 22.5px;
                color: rgba(51, 51, 51, 1);
                text-align: left;
                margin-left: 12px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                margin-top: -3px;
            }

            .platformhome-bottom-all-right-zhong {
                margin-left: 12px;
                margin-top: 2.5px;

                .platformhome-bottom-all-right-zhong-one {
                    height: 20px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    color: rgba(51, 51, 51, 1);
                }

                .platformhome-bottom-all-right-zhong-two {
                    height: 20px;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    color: rgba(51, 51, 51, 1);
                    text-align: left;
                }
            }


            .platformhome-bottom-all-right-small-two {
                height: 16.5px;
                font-family: PingFangSC-Regular;
                font-size: 12px;
                font-weight: 400;
                line-height: 16.5px;
                color: rgba(153, 153, 153, 1) !important;
                text-align: left;
            }



            .platformhome-bottom-all-right-sur {
                display: flex;
                justify-content: space-between;
                margin-left: 12px;

                .platformhome-bottom-all-right-sur-one-a {
                    display: inline-block;
                    // width: 19.5px;
                    height: 20px;
                    font-family: DINAlternate-Bold;
                    font-size: 20px;
                    font-weight: 700;
                    line-height: 20px;
                    color: rgba(255, 88, 0, 1);
                    text-align: left;
                    margin-top: 2.9px;
                }

                .platformhome-bottom-all-right-sur-one-b {
                    display: inline-block;
                    // width: 42px;
                    height: 12px;
                    font-family: PingFangSC-Semibold;
                    font-size: 12px;
                    font-weight: 700;
                    line-height: 12px;
                    color: rgba(255, 88, 0, 1);
                    text-align: left;
                    margin-left: 3px;
                }
                .platformhome-bottom-all-right-sur-two{
                    display: flex;
                    padding-top: 2px;
                }

                .platformhome-bottom-all-right-sur-two-a,
                .platformhome-bottom-all-right-sur-two-b {
                    display: inline-block;
                    width: 66px;
                    height: 26px;
                    border: 1px solid rgba(0, 198, 130, 1);
                    border-radius: 4px;
                    font-family: PingFangSC-Semibold;
                    font-size: 12px;
                    font-weight: 700;
                    color: #00C682;
                    text-align: center;
                    line-height: 26px;
                }

                .platformhome-bottom-all-right-sur-two-b {
                    margin-left: 8px;
                }
            }
        }

    }


    
        .adm-tabs-header {
            border: none;
        }

        .adm-tabs-tab {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            font-weight: 400;
            color: rgba(102, 102, 102, 1);
        }

        .adm-tabs-tab-active {
            font-family: PingFangSC-Semibold;
            font-size: 17px;
            font-weight: 700;
            color: rgba(51, 51, 51, 1);
        }

        .adm-tabs-tab-line {
            width: 21px !important;
            height: 5px !important;
            background: url("#{$wos-cdn}amvdre58__w85_h20.png") no-repeat;
            background-size: cover;
            bottom: 1px;
        }

        .platformhome-bottom .adm-tabs-tab-wrapper-stretch {
            flex: none !important;
            padding: 0 16px;
        }




    .platformhome-bottom-tbs {
        display: flex;
        justify-content: space-between;
        position: sticky;
        top: 0px;
        z-index: 999;
        background: rgba(255, 255, 255, 1);
        height: 48px;
        align-items: center;
        // width: 100% !important;
    }

    .platformhome-bottom .adm-tabs {
        width: 100%;
    }

    .platformhome-bottom-all-list {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-top: 10px;
    }

    .platformhome-bottom-poslvie {
        // background: yellow;
        position: relative;
        display: flex;
        justify-content: center;
    }

    .platformhome-bt-more {
        width: 100%;
        height: 38px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        font-weight: 400; 
        color: rgba(153, 153, 153, 1);
        position: absolute;
        bottom: -12px;
    }


    .platformhome-bt-img {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: url('#{$wos-cdn}es6v6ku7__w32_h32.png') no-repeat;
        background-size: cover;

    }

    .platformhome-bottom-all-right-zhong-map {
        height: 40px;
        // background: red;
        display: flex;
    }
    .share-text {
        width: 0;
        height: 0;
        font-size: 12px;
        opacity: 0;
    }

    .placard-modal {
        width: 100%;
        height: 100%;
        .adm-center-popup-wrap{
            background-color: transparent;
            min-width: 80%;
            max-width: 80%;
            top: 51%;
        }
        .adm-center-popup-body{
            --background-color:transparent;

        }
        .adm-modal-body, .adm-modal-content {
            max-height: 90vh;
            height: 90vh;
            padding: 0;
            border-radius: 0;
        }

        .placard-body {
            width: 100%;
            height: 100%;

        }
        
        .placard-pic {
            display: block;
            max-width: 100%;
            max-height: calc(100% - 80px);
            margin: 0 auto;
            width: 100%;
        }

        .modal-tip {
            font-size: 14px;
            font-weight: 700;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
            text-align: center;
            margin: 8px 0 20px 0;
        }

        .placard-refresh-btn {
            display: block;
            width: 230px;
            height: 48px;
            background-color: rgba(0, 198, 130, 1);
            border: 1px solid rgba(0, 198, 130, 1);
            border-radius: 36px;
            font-family: PingFangSC-Medium;
            font-size: 16px;
            font-weight: 500;
            line-height: 20px;
            color: #fff;
            text-align: center;
            margin: 0 auto;

            span {
                font-family: PingFangSC-Semibold;
                font-size: 12px;
                font-weight: 700;
                line-height: 16.5px;
            }
        }

        .icon-close {
            display: block;
            width: 28px;
            height: 28px;
            margin: 28px auto 0;
            background: transparent url("#{$wos-cdn}rfgtv73c__w112_h112.png") no-repeat;
            background-size: cover;
        }
    }
}