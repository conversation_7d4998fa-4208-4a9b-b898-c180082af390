import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Radio, Popup ,Checkbox} from 'antd-mobile-v5'

import { bridge } from '@sqb/utility';
import { Toast } from 'antd-mobile';
import request from 'api/request';
import { API } from 'api/constants';
import rules from 'api/rules'
import {
    allColumns,
    treatColumns,
    stopColumns,
    personTypeColumns
} from './const'
import "./index.scss"
import { isArray } from 'lodash';

const MemberConfiguration = (props) => {
    const { state } = props.location
    const [form] = Form.useForm()
    const { getFieldValue } = form
    const [visible, setVisible] = useState(false)
    const [saleStatus, setSaleStatus] = useState('')
    const [value, setValue] = useState('SIGNED')

    const [personTypeVisible, setPersonTypeVisible] = useState(false)
    const [personTypeValue, setPersonTypeValue] = useState<string>("")
    //https://docs.58corp.com/#/space/1795003080983490560
    //用来判断 人员状态  是否可编辑 ，总结为：在编辑页面时 都为true 只读 false 可编辑，新增页面时：人员状态 可编辑 ， 20240624犇犇需求一期（pm：王福荣）
    const [personTypeIsHasValue, setPersonTypeIsHasValue] = useState<boolean>()

    const [teamProductVisible, setTeamProductVisible] = useState(false)
    const [teamProductValue, setTeamProductValue] = useState([])
    const [teamProductList, setTeamProductList] = useState<any>([])
    const [isNeedPartTime, setIsNeedPartTime] = useState<number>(0);//	是否需要兼职营销员 0-不需要 1-需要
    const [teamProductIsEdit, setTeamProductIsEdit] = useState<boolean>(true)//团队产品是否可编辑  根据接口seniorSaleId判断，null为可编辑
    
    const [referee, setReferee] = useState<string>("");//推荐人 
    const [isShowReferrersCommissionRate, setIsShowReferrersCommissionRate] = useState(false);//是否显示推荐人分佣比例
    useEffect(() => {
        bridge.setTitle('成员配置'); 
        teamProductePickerMethod()
    
        const fetchData = async () => {
            const [productList  = [], productConfiguration = {}] = await Promise.all([getTeamProductList(),  getConfig()])
              if (productConfiguration === 1) { //	新增产品是否配置到团队/个人(0-否,1-是)
                console.log(productList,"productListproductListproductList")
              let teamProduct = []
              isArray(productList) && productList.forEach(element => {
                  teamProduct.push(element.productAndTypeId);
              });
              setTeamProductValue(teamProduct);
            }
          }
          if (state.action === 'edit') {
             getTeamProductList();
             getConfig();
        }else{
            fetchData()
        }
    
    }, [])
    useEffect(() => {
        if (state.action === 'edit') {
            echo()
        }
    }, [])
    const echo = () => {
    
        request.get(API.getSalesTeam, { id: state.id }).then(res => {
            const { rCode, data ,rMsg } = res || {};
            console.log(data.saleStatus,"--saleType");
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            setValue(data.saleStatus)
            setSaleStatus(data.saleStatus)
            let teamProduct = [];
            isArray(data.productIdList) && data.productIdList.forEach(element => {
                teamProduct.push(element.productAndTypeId);
            });
            setReferee(data.referee);
            setTeamProductValue(teamProduct)
            setPersonTypeValue(data.saleType?data.saleType:"")
            setPersonTypeIsHasValue(data.saleType && data.saleType.length > 0);
            setTeamProductIsEdit(data.seniorSaleId === null)
            form.setFieldsValue(data)
        })
    }

    useEffect(() => {
     
        if (isNeedPartTime === 1 && personTypeValue === "PARTTIME" && referee && referee.length > 0) {
            // 兼职 + 推荐人 显示 推荐人分佣比例
            setIsShowReferrersCommissionRate(true);
        }else{
            setIsShowReferrersCommissionRate(false);
            
        }
    }, [personTypeValue,isNeedPartTime,referee])
    const getConfig = () => {
        return request.get(API.queryJudgeCondition).then((res) => {
            const { rCode, data } = res || {};
            if (rCode === 0) {
                const {isNeedPartTime ,productConfiguration} = data;
                setIsNeedPartTime(isNeedPartTime);
                return productConfiguration;
            }
        });
    };
    //获取成员 - 团队产品
    const getTeamProductList = () => {
        return request.get(API.getTeamProductList, { id: state.id }).then(res => {
            const { rCode, data ,rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            setTeamProductList(data)
            return data
        })
    }
    const inputParams =  (values) => {
        let productIdList = [];
     isArray(teamProductValue) && teamProductValue.forEach(item => {
        isArray(teamProductList) && teamProductList.forEach(element => {
                if (item === element.productAndTypeId) {
                    productIdList.push(element) 
                }
            });  
        });

        const params = {
            ...values,
            saleStatus: value,
            saleType: personTypeValue?personTypeValue:"BIZ_STAFF",
            productIdList:productIdList,
        }
        return params;
    }

    const onSubmit = () => {
        // const values = form.getFieldsValue()
      
        form.validateFields().then((values) => {

            if (isNeedPartTime === 1 && personTypeValue.length === 0) {
                Toast.info('请选择人员类型');
                return;
            }
            if (isShowReferrersCommissionRate === true) {
                
                if (!(!isNaN(values.referrersCommissionRate) && values.referrersCommissionRate >= 0 && values.referrersCommissionRate <= 100)) {
                    Toast.info('推荐人分佣比例请输入0-100的数字');
                    return 
                } else {
                   
                }
            }

            if (state.action === 'add') {
           
                const params = inputParams(values)
                console.log(params,"--params")
                request.post(API.insertSales, params).then(res => {
                    const { rCode  ,rMsg} = res || {};
                    if (rCode === 0) {
                        props.history.push('/team')
                    }else{
                        Toast.info(rMsg || '系统错误,请稍后重试');
                    }

                })
            } else {
      
                let params = inputParams(values)
                params = {...params , id:state.id}
                delete params.saleMobile
                console.log(params,"--params")
                request.post(API.updateSales, params).then(res => {
                    const { rCode  ,rMsg} = res || {};
                    if (rCode === 0) {
                        props.history.push('/team')
                    }else{
                        Toast.info(rMsg || '系统错误,请稍后重试');
                    }

                })
            }

        })
            .catch((err) => {
                console.log(err, 'rrrr')
            })

    }
    const popupAction = (val) => {
        setValue(val)
        setVisible(false)
    }
    const popupPersonTypeAction = (val) => {
        setPersonTypeValue(val)
        setPersonTypeVisible(false)
    }
    const popupTeamProductsAction = (val) => {
       setTeamProductValue(val);
    }
    const pickerMethod = () => {
        let str = ''
        allColumns.forEach(item => {
            if (item.value === value) {
                str = item.label
            }
        })
        return str
    }
    const personTypePickerMethod = () => {
        let str = ''
        personTypeColumns.forEach(item => {
            if (item.value === personTypeValue) {
                str = item.label
            }
        })
        return str
    }
    const teamProductePickerMethod = () => {
        let str = ''
        isArray(teamProductList) && teamProductList.forEach(item => {
            isArray(teamProductValue) && teamProductValue.forEach(product => {
                if (item.productAndTypeId === product) {
                    str = str.length > 0 ? str + "," + item.teamProductName : item.teamProductName
                } }
            )
        })
        return str
    }
    // const refereeDisabled = () => {
    //     if(state.action === 'edit' && !personTypeIsHasValue)  {
    //         return false;
    //     }
    //     return true
    // }

    return <div className='signing-body'>
        <div className='signing-body-header-center'>
            <div className='signing-body-header-form-top'>
                <Form form={form} layout='horizontal'>
                    <Form.Item label='姓名' name='saleName'
                        rules={[{ 
                            validator: (rule, value, callback) => {
                                if(!value){
                                    callback('请输入姓名')
                                }else if(/\s/.test(value)){
                                        callback('请输入合法姓名')
                                }else{
                                    callback()
                                }
                            }
                         }]}
                    >
                        <Input placeholder='请输入'
                            style={{ color: '#33333' }} />
                    </Form.Item>
                    <Form.Item label='手机号' name='saleMobile'
                        rules={[
                        {
                            validator: (rule, value, callback) => {
                                if(!value){
                                    callback('请输入手机号')
                                }else if(state.action === 'add' && !rules.isMobilePhone(value)){
                                        callback('手机号格式不正确')
                                }else{
                                    callback()
                                }
                            }
                        }
                        ]}
                    >
                        <Input type='tel' placeholder='请输入'  style={{ color: '#33333' }} disabled={state.action === 'edit' && true} />
                    </Form.Item>
                    <Form.Item label='签约状态' onClick={() => state.action !== 'add' && setVisible(true)} style={{ color: '#33333' }}>
                        <Popup
                            visible={visible}
                            onMaskClick={() => setVisible(false)}
                        >
                            <div className='popup-title'>
                                <div className='popup-top'>
                                    <p className='popup-left'>签约状态</p>
                                    <p className='popup-right' onClick={() => setVisible(false)}></p>
                                </div>
                                <div className='popup-bottom'>
                                    <Radio.Group defaultValue={value} onChange={(val) => { popupAction(val) }}>
                                        {
                                            saleStatus === 'WAITING_AUDIT' ?
                                                treatColumns.map(item => {
                                                    return <div key={item.value} className='popup-bottom-p'>
                                                        <Radio className='popup-bottom-p-radio' value={item.value} > {item.label}  </Radio>
                                                    </div>
                                                })
                                                : saleStatus === 'SIGNED' &&
                                                stopColumns.map(item => {
                                                    return <div key={item.value} className='popup-bottom-p'>
                                                        <Radio className='popup-bottom-p-radio' value={item.value} > {item.label}  </Radio>
                                                    </div>
                                                })
                                        }

                                    </Radio.Group>
                                </div>
                            </div>
                        </Popup>
                        <div style={{ fontSize: '14px' }}>{pickerMethod()} </div>
                    </Form.Item>
                    {isNeedPartTime === 1 ? <>   <Form.Item label='人员类型'  onClick={() => {
                        if(state.action === 'edit' && personTypeIsHasValue)  {
                            return
                        }
                        setPersonTypeVisible(true)
                    } } style={{ color: '#33333' }} 
                      >
                        <Popup
                            visible={personTypeVisible}
                            onMaskClick={() => setPersonTypeVisible(false)}
                        >
                            <div className='popup-title'>
                                <div className='popup-top'>
                                    <p className='popup-left'>人员类型</p>
                                    <p className='popup-right' onClick={() => setPersonTypeVisible(false)}></p>
                                </div>
                                <div className='popup-bottom'>
                                    <Radio.Group defaultValue={personTypeValue} onChange={(val) => { popupPersonTypeAction(val) }}>
                                        {
                                                personTypeColumns.map(item => {
                                                    return <div key={item.value} className='popup-bottom-p'>
                                                        <Radio className='popup-bottom-p-radio' value={item.value} > {item.label}  </Radio>
                                                    </div>
                                                })
                                        }

                                    </Radio.Group>
                                </div>
                            </div>
                        </Popup>
                        <div style={{ fontSize: '14px' }}>{personTypePickerMethod()} </div>
                    </Form.Item>
                    <Form.Item label='推荐人' name='referee'
                    >
                        <Input placeholder='无' style={{ color: '#333333' }} type='referee' disabled={true} />
                    </Form.Item>
                    </> : null}
                 
                      <Form.Item  hidden={!isShowReferrersCommissionRate} label='推荐人分佣比例(%)' name='referrersCommissionRate'>
                         <Input  placeholder='请输入0-100区间的数字' style={{ color: '#33333' ,marginTop : "6px"}} /> 
                    </Form.Item> 
                 
                    <Form.Item label='团队产品' disabled={false} onClick={() => teamProductIsEdit && setTeamProductVisible(true)} style={{ color: '#33333' }}>
                        <Popup
                    
                            visible={teamProductVisible}
                            onMaskClick={() => setTeamProductVisible(false)}
                        >
                            <div className='popup-title'>
                                <div className='popup-top'>
                                    <p className='popup-left'>团队产品</p>
                                    <p className='popup-right' onClick={() => setTeamProductVisible(false)}></p>
                                </div>
                                <div className='popup-bottom'>
                                    <Checkbox.Group defaultValue={teamProductValue} onChange={(checked) => { popupTeamProductsAction(checked) }}>
                                        {
                                        Array.isArray(teamProductList) && teamProductList.map(item => {
                                                return <div key={item.productAndTypeId} className='popup-bottom-p'>
                                                      {/* <Checkbox className='popup-bottom-p-checkbox' value={item.productAndTypeId} > {item.teamProductName}  </Checkbox> */}
                                                      <Checkbox className='popup-bottom-p-checkbox' 
                                                      icon={checked =>
                                                        checked ? (<div> <img className='checkImage'  src="//j1.58cdn.com.cn/jinrong/images/ems1720598721593cf3596abb07da.png" alt="" /></div>) 
                                                        : ( <div> <img   className='checkImage' src="//j1.58cdn.com.cn/jinrong/images/ems1720598738612081c3194cd40e.png" alt="" /></div>)}
                                                      value={item.productAndTypeId} > {item.teamProductName}  </Checkbox>
                                                 </div>
                                             })
                                        }

                                    </Checkbox.Group>
                                </div>
                            </div>
                        </Popup>
                        <div style={{ fontSize: '14px' }}>{teamProductePickerMethod()} </div>
                    </Form.Item>
                </Form>
            </div>
        </div>
        <div className='signing-body-bottom-button'>
            <Button block color='primary' size='large' onClick={() => { onSubmit() }} > 确定 </Button>
        </div>
    </div>;

};
export default MemberConfiguration;