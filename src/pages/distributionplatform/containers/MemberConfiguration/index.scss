.signing-body {
    width: 100%;
    height: 100%;
    background-color: rgba(247, 247, 247, 1);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .signing-body-header-center {
        flex: 1;

        .signing-body-header-form-top {
            background-color: rgba(255, 255, 255, 1);
            border-radius: 10px;
            margin: 12px;

            .adm-list-header {
                padding: 12px 0 12px 12px;
                font-size: 18px;
                font-weight: 700;
                line-height: 18px;
                color: rgba(51, 51, 51, 1);
                font-family: PingFangSC-Semibold;
            }

            .adm-list-body {
                .adm-list-body-inner {
                    .adm-list-item {
                        padding: 0 12px;
                        .adm-list-item-content{
                            border-bottom: 1px solid rgba(247, 247, 247, 1);
                            .adm-form-item-required-asterisk{
                                display: none;
                            }
                            .adm-list-item-content-arrow{
                                width: 14px;
                            }
                            }
                        .adm-list-item-content-prefix {
                            padding: 17px 0;
                            font-size: 14px;
                            font-weight: 700;
                            color: rgba(51, 51, 51, 1);
                            font-family: PingFangSC-Semibold;
                        }

                        .adm-list-item-content-main {
                            padding: 17px 0;
                            .adm-list-item-description{
                                color: red;
                            }
                            .adm-input-element{
                                font-family: PingFangSC-Regular;
                            }
                            input::placeholder {
                                font-size: 14px;
                                font-weight: 400;
                                color: rgba(153, 153, 153, 1);
                            }
                        }
                    }
                }
            }
        }
    }

    .signing-body-bottom-button {
        height: 48px;
        background-color: rgba(0, 198, 130, 1);
        border-radius: 25px;
        color: rgba(255, 255, 255, 1) ;
        margin: 8px 12px;
        font-family: PingFangSC-Medium;
        font-size: 17px;
        font-weight: 500;
        button {
            height: 100%;
        }
    }
}
.popup-title{
    border-radius: 6px 6px 0px 0px;
    background: #fff;
    @supports (bottom: constant(safe-area-inset-bottom)) {
        padding-bottom:constant(safe-area-inset-bottom);
    }
    
    @supports (bottom: env(safe-area-inset-bottom)) {
        padding-bottom:env(safe-area-inset-bottom);
    }
    
    .popup-top{
        display: flex;
        justify-content: space-between;
        .popup-left{
            padding: 25px 0 12.5px 16px;
            font-family: PingFangSC-Medium;
            font-size: 20px;
            font-weight: 500;
            color: rgba(51, 51, 51, 1);
        }
        .popup-right{
            width: 28px;
            height: 28px;
            margin: 25px 16px 12.5px 0;
            background-image: url("//wos.58cdn.com.cn/cDazYxWcDHJ/picasso/upcah6pr__w56_h56.png");
            background-repeat: no-repeat;
        }
    }
    .popup-bottom{
        max-height: 300px;
        overflow: auto;
        .popup-bottom-p{
            height: 54.5px;
            .checkImage{
                width: 20px;
                height: 20px;
                margin-right: 16px;
            }
            .adm-radio{
                display: flex;
                flex-direction: row-reverse;
                height: 100%;
                justify-content: space-between;
                align-items: center;
                .adm-radio-content{
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(51, 51, 51, 1);
                    padding-left: 16px;
                }
              
            }
            .adm-radio-checked{
                .adm-radio-icon{
                    background-color: rgba(0, 198, 130, 1);
                    color: #fff;
                    margin-right: 16px;
                }
            }
            .adm-checkbox{
                display: flex;
                flex-direction: row-reverse;
                height: 100%;
                justify-content: space-between;
                align-items: center;
                .adm-checkbox-content{
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(51, 51, 51, 1);
                    padding-left: 16px;
                }
            //     .adm-checkbox-icon{
            //             background-color: white;
            //             color: #fff;
            //             margin-right: 16px;
            //             border-radius: 5px;
            //             border: 0.5px solid #a7a3a3
            //    }
            }
            // .adm-checkbox-checked{
            //     .adm-checkbox-icon{
            //         background-color: rgba(0, 198, 130, 1);
            //         color: #fff;
            //         margin-right: 16px;
            //     }
            // }
        }
    }
}