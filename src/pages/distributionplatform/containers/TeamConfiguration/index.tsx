import React, { useEffect, useState, RefObject, useRef } from 'react';
import { Form, Input, Button, Radio, Popup, Picker, Dialog, Checkbox } from 'antd-mobile-v5'
import { bridge } from '@sqb/utility';
import { Toast } from 'antd-mobile';
import request from 'api/request';
import { API } from 'api/constants';
import rules from 'api/rules';
import { desensitizePhoneNumber } from 'tools/utils';
import type { DatePickerRef } from 'antd-mobile-v5';
import {
    allColumns,
    treatColumns,
    stopColumns
} from './const'
import "./index.scss"
import { isArray } from 'lodash';
export const basicColumns =
    [[
        { label: '无效', value: '0' },
        { label: '有效', value: '1' },

    ]]
const isEmpty = (val: string | number | undefined | null) => {
    if (val === null || val === undefined || val === "") {
        return true
    }
    return false
}
const MemberConfiguration = (props) => {
    const { state = { action: "edit" } } = props.location;
    console.log(state, "state");
    // 修改时 需要脱敏 这个地方用来保存真实的手机号
    const mobileRef = useRef<string>("")
    const [form] = Form.useForm()
    const [visible, setVisible] = useState(false)
    const [saleStatus, setSaleStatus] = useState('')
    const [value, setValue] = useState('SIGNED')

    const [teamProductVisible, setTeamProductVisible] = useState(false)
    const [teamProductValue, setTeamProductValue] = useState([])
    const [teamProductList, setTeamProductList] = useState<any>([])
    // 贷款产品每日可分享扫码次数,字段展示开关
    const [isShowLoanProductShareNum, setIsShowLoanProductShareNum] = useState<boolean>(false);


    useEffect(() => {
        bridge.setTitle('团队配置');
        const fetchData = async () => {
            const [productList = [], productConfiguration = {}] = await Promise.all([getTeamProductList(), getConfig()])
            if (productConfiguration === 1) { //	新增产品是否配置到团队/个人(0-否,1-是)
                let teamProduct = []
                isArray(productList) && productList.forEach(element => {
                    teamProduct.push(element.productAndTypeId);
                });
                setTeamProductValue(teamProduct);
            }
        }
        if (state.action !== 'edit') {
            fetchData()
        } else {
            getTeamProductList();
        }

    }, [])

    // 监控是否选中贷款产品来展示《贷款产品每日可分享扫码次数》
    useEffect(() => {
        if (teamProductList.length > 0 && teamProductValue.length > 0) {
            let flag = false;
            teamProductList.forEach(item => {
                // 是贷款产品 并且 在选中的产品列表中
                if (teamProductValue.includes(item.productAndTypeId) && item.productCategoryEnum === 1) {
                    flag = true;
                }
            });
            setIsShowLoanProductShareNum(flag);
        }
    }, [teamProductList, teamProductValue])
    useEffect(() => {
        if (state.action === 'edit') {
            echo()
        }
    }, [])
    const echo = () => {
        request.get(API.getTeamByOne, { id: state.id }).then(res => {
            console.info('getTeamByOneRes', res);
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            setValue(data.saleStatus)
            setSaleStatus(data.saleStatus)
            let teamProduct = [];
            isArray(data.orgProductList) && data.orgProductList.forEach(element => {
                teamProduct.push(element.productAndTypeId);
            });
            setTeamProductValue(teamProduct);
            mobileRef.current = data.teamLeaderMobile;
            data.teamLeaderMobile = desensitizePhoneNumber(data.teamLeaderMobile)
            if (isEmpty(data.orgStatus)) {
                delete data.orgStatus
            } else {
                data.orgStatus = [data.orgStatus + ""]
            }
            form.setFieldsValue(data)

        })
    }
    const inputParams = (values) => {
        let productIdList = [];
        isArray(teamProductValue) && teamProductValue.forEach(item => {
            isArray(teamProductList) && teamProductList.forEach(element => {
                if (item === element.productAndTypeId) {
                    productIdList.push(element)
                }
            });
        });

        const params = {
            ...values,
            saleStatus: value,
            orgProductList: productIdList
        }
        return params;
    }

    const getConfig = async () => {
        return await request.get(API.queryJudgeCondition).then((res) => {
            const { rCode, data } = res || {};
            if (rCode === 0) {
                const { productConfiguration } = data;
                return productConfiguration
            }
        });
    };
    const onSubmit = (values) => {
        // const values = form.getFieldsValue()
        values.orgStatus = values.orgStatus[0]
        if (state.action === 'add') {
            // const params = {
            //     ...values,
            //     saleStatus: value,
            //     orgProductList:teamProductValue
            // }
            const params = inputParams(values);
            request.post(API.insertTeam, params).then(res => {
                const { rCode, rMsg } = res || {};
                if (rCode === 0) {
                    props.history.push('/channel')
                } else {
                    Toast.info(rMsg || '系统错误,请稍后重试');
                }

            })
        } else {
            // const params = {
            //     ...values,
            //     id: state.id,
            //     saleStatus: value,
            //     orgProductList:teamProductValue
            // }
            let params = inputParams(values);
            params = { ...params, id: state.id };
            if (params.teamLeaderMobile.includes("****")) {
                // 如果是直接回显的脱敏的手机号码 则使用该号码脱敏前的手机号码
                // 否则则说明用户手动输入了新的号码 直接取表单的value
                params.teamLeaderMobile = mobileRef.current
            }
            request.post(API.updateTeam, params).then(res => {
                const { rCode, rMsg } = res || {};
                if (rCode === 0) {
                    props.history.push('/channel')
                } else {
                    Toast.info(rMsg || '系统错误,请稍后重试');
                }

            })
        }

    }
    const preSubmit = async () => {
        try {
            const params = await form.validateFields();
            console.log(params, "params");

            if (`${params.orgStatus}` === "0") {
                // 无效的时候需要加提示
                Dialog.confirm({
                    title: "提示",
                    content: '团队状态无效！该团队成员将失去组织，且无法进行成员拓展、产品分享。请谨慎操作！',
                    cancelText: "在想想",
                    onConfirm: () => {
                        console.log('Confirmed')
                        onSubmit(params)
                    },
                    onClose: () => {
                        console.log("cancel")
                    }
                });
                return
            }
            onSubmit(params)

        } catch (e) {
            console.log(e);

        }

    }
    const popupAction = (val) => {
        setValue(val)
        setVisible(false)
    }
    const pickerMethod = () => {
        let str = ''
        allColumns.forEach(item => {
            if (item.value === value) {
                str = item.label
            }
        })
        return str
    }
    const popupTeamProductsAction = (val) => {
        console.log(val, "val")
        setTeamProductValue(val);
    }
    //获取团队 - 团队产品
    const getTeamProductList = async () => {
        return await request.get(API.getTeamProductList, { id: state.id }).then(res => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            setTeamProductList(data)
            return data
        })
    }
    const teamProductePickerMethod = () => {
        let str = ''
        console.log(teamProductList, "0--teamProductList");
        isArray(teamProductList) && teamProductList.forEach(item => {
            isArray(teamProductValue) && teamProductValue.forEach(product => {
                if (item.productAndTypeId === product) {
                    str = str.length > 0 ? str + "," + item.teamProductName : item.teamProductName
                }
            }
            )
        })
        return str
    }
    const handleChangeShareNumber = (value) => {
        if (value !== '') {
            form.setFieldValue('shareNumber', Number(value.replace(/[^\d]/g, '')));
        }
    }

    return <div className='signing-body'>
        <div className='signing-body-header-center'>
            <div className='signing-body-header-form-top'>
                <Form form={form} layout='horizontal'>
                    <Form.Item label='团队名称' name='orgName'
                        rules={[{
                            validator: (rule, value, callback) => {
                                if (!value) {
                                    callback('请输入团队名称')
                                } else if (/\s/.test(value)) {
                                    callback('请输入合法团队名称')
                                } else {
                                    callback()
                                }
                            }
                        }]}
                    >
                        <Input placeholder='请输入'
                            style={{ color: '#33333' }} />
                    </Form.Item>
                    <Form.Item label='姓名' name='teamLeaderName'
                        rules={[{
                            validator: (rule, value, callback) => {
                                if (!value) {
                                    callback('请输入姓名')
                                } else if (/\s/.test(value)) {
                                    callback('请输入合法姓名')
                                } else {
                                    callback()
                                }
                            }
                        }]}
                    >
                        <Input placeholder='请输入'
                            style={{ color: '#33333' }} />
                    </Form.Item>
                    <Form.Item label='手机号' name='teamLeaderMobile'
                        rules={[{
                            validator: (rule, value, callback) => {
                                if (!value) {
                                    callback('请输入手机号')
                                } else if (state.action === 'add' && !rules.isMobilePhone(value)) {
                                    callback('手机号格式不正确')
                                } else {
                                    callback()
                                }
                            }
                        }]}
                    >
                        <Input type='tel' placeholder='请输入' />
                    </Form.Item>
                    <Form.Item label='签约状态' onClick={() => state.action !== 'add' && setVisible(true)} style={{ color: '#33333' }}>
                        <Popup
                            visible={visible}
                            onMaskClick={() => setVisible(false)}
                        >
                            <div className='popup-title'>
                                <div className='popup-top'>
                                    <p className='popup-left'>签约状态</p>
                                    <p className='popup-right' onClick={() => setVisible(false)}></p>
                                </div>
                                <div className='popup-bottom'>
                                    <Radio.Group defaultValue={value} onChange={(val) => { popupAction(val) }}>
                                        {
                                            saleStatus === 'WAITING_AUDIT' ?
                                                treatColumns.map(item => {
                                                    return <div key={item.value} className='popup-bottom-p'>
                                                        <Radio className='popup-bottom-p-radio' value={item.value} > {item.label}  </Radio>
                                                    </div>
                                                })
                                                : (saleStatus === 'SIGNED' || saleStatus === "TERMINATED") &&
                                                stopColumns.map(item => {
                                                    return <div key={item.value} className='popup-bottom-p'>
                                                        <Radio className='popup-bottom-p-radio' value={item.value} > {item.label}  </Radio>
                                                    </div>
                                                })
                                        }

                                    </Radio.Group>
                                </div>
                            </div>
                        </Popup>
                        <div style={{ fontSize: '14px' }}>{pickerMethod()} </div>
                    </Form.Item>
                    <Form.Item
                        label='团队产品'
                        disabled={false}
                        onClick={() => setTeamProductVisible(true)}
                        style={{ color: '#33333' }}
                    >
                        <Popup
                            visible={teamProductVisible}
                            onMaskClick={() => setTeamProductVisible(false)}
                        >
                            <div className='popup-title'>
                                <div className='popup-top'>
                                    <p className='popup-left'>团队产品</p>
                                    <p className='popup-right' onClick={() => setTeamProductVisible(false)}></p>
                                </div>
                                <div className='popup-bottom'>
                                    <Checkbox.Group
                                        defaultValue={teamProductValue}
                                        onChange={(checked) => { popupTeamProductsAction(checked) }}
                                    >
                                        {
                                            Array.isArray(teamProductList) && teamProductList.map(item => {
                                                return <div key={item.productAndTypeId} className='popup-bottom-p'>
                                                    {/* <Checkbox className='popup-bottom-p-checkbox' value={item.productAndTypeId} > {item.teamProductName}  </Checkbox> */}
                                                    <Checkbox
                                                        className='popup-bottom-p-checkbox'
                                                        icon={checked =>
                                                            checked ? (<div> <img className='checkImage' src="//j1.58cdn.com.cn/jinrong/images/ems1720598721593cf3596abb07da.png" alt="" /></div>)
                                                                : (<div> <img className='checkImage' src="//j1.58cdn.com.cn/jinrong/images/ems1720598738612081c3194cd40e.png" alt="" /></div>)}
                                                        value={item.productAndTypeId}
                                                    >
                                                        {item.teamProductName}
                                                    </Checkbox>
                                                </div>
                                            })
                                        }

                                    </Checkbox.Group>
                                </div>
                            </div>
                        </Popup>
                        <div style={{ fontSize: '14px' }}>{teamProductePickerMethod()} </div>
                    </Form.Item>
                    {isShowLoanProductShareNum &&
                        <Form.Item
                            name='shareNumber'
                            label='贷款产品每日可分享扫码次数'
                            className='form_item_share_number'
                        >
                            <Input
                                placeholder='请输入'
                                type="tel"
                                min={0}
                                onChange={handleChangeShareNumber}
                            />
                        </Form.Item>
                    }
                    <Form.Item
                        name='orgStatus'
                        label='团队状态'
                        trigger='onConfirm'
                        initialValue={["1"]}
                        onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                            datePickerRef.current?.open()
                        }}
                    >
                        <Picker
                            columns={basicColumns}
                        >
                            {(value: any) =>
                                value && value.length > 0 ? value[0]?.label : <Input readOnly placeholder='请选择' />
                            }
                        </Picker>

                    </Form.Item>
                </Form>
            </div>
        </div>
        <div className='signing-body-bottom-button'>
            <Button block color='primary' size='large' onClick={preSubmit} > 确定 </Button>
        </div>
    </div>;

};
export default MemberConfiguration;