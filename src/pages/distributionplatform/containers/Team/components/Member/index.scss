@import "~scss/index";
.member-body{
    .adm-list-item:last-child{
        .adm-list-item-content{
            border-bottom: none;
        }
    }
   
    .adm-list-item-content{
        border-bottom: 0.5px solid rgba(229, 229, 229, 1);
        height: 80px;
        display: flex;
        align-items: center;
        margin: 0 16px 0 12px;
        .adm-form-item-child-inner{
            display: flex;
            justify-content: space-between;
            align-items: center;
            .member-body-left{
                .member-body-left-name{
                    font-family: PingFangSC-Semibold;
                    font-size: 14px;
                    font-weight: 700;
                    color: rgba(51, 51, 51, 1);
                }
                .member-body-left-phone{
                    padding-top: 8.5px;
                    font-family: PingFangSC-Regular;
                    font-size: 12px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                }
            }
            .member-body-rigth1{
                font-family: PingFangSC-Regular;
                font-size: 14px;
                font-weight: 400;
                color: rgba(0, 198, 130, 1);
            }
            .member-body-rigth2{
                font-family: PingFangSC-Regular;
                font-size: 14px;
                font-weight: 400;
                color: rgba(153, 153, 153, 1);
            }
        }
    }

    .adm-list-item-content-arrow{
        width: 14px;
        height: 14px;
        svg{
            display: none;
        }
        background: url("#{$wos-cdn}repc2bd5__w56_h56.png") no-repeat;
        background-size: cover;
    }
}