import React, { useEffect, useState, useRef } from 'react';
import { Form, Input, Button, Radio, Popup } from 'antd-mobile-v5'
import './index.scss'


const Index = (props: any) => {
    const tableThead = [
        {
            theadTitle: '姓名',
        },
        {
            theadTitle: '本日保单'
        },
        {
            theadTitle: '本月保单'
        },
        {
            theadTitle: '本日保费'
        },
        {
            theadTitle: '本月保费'
        },
    ]
    const { tableData, amountData } = props
    return <div className='performance-table'>
        <table style={{ width: '100%',borderCollapse:'collapse' }}>
            <thead style={{ height: '36px' }}>
                <tr className='performance-table-thead-tr'>
                    {
                        tableThead.map(item => {
                            return <th className='performance-table-thead-th'> {item?.theadTitle} </th>
                        })
                    }
                </tr>
            </thead>
            <tbody>
                {/* <tr className='performance-table-tbody-tr'>
                    <td className='performance-table-tbody-tb'> 合计 </td>
                    <td className='performance-table-tbody-num number-font'> {amountData?.todayPolicyCountSum} </td>
                    <td className='performance-table-tbody-num number-font'> {amountData?.monthPolicyCountSum} </td>
                    <td className='performance-table-tbody-num number-font'> {amountData?.todayPremiumSum} </td>
                    <td className='performance-table-tbody-num number-font'> {amountData?.monthPremiumSum} </td>
                </tr> */}
                {
                    tableData && tableData.length ? tableData.map(item => {
                        return <tr className='performance-table-tbody-tr'>
                            <td className='performance-table-tbody-tb'> {item?.saleName} </td>
                            <td className='performance-table-tbody-num number-font'> {item?.todayPolicyCount} </td>
                            <td className='performance-table-tbody-num number-font'> {item?.monthPolicyCount} </td>
                            <td className='performance-table-tbody-num number-font'> {item?.todayPremium} </td>
                            <td className='performance-table-tbody-num number-font'> {item?.monthPremium} </td>
                        </tr>
                    }) : <></>
                }
            </tbody>
        </table>
    </div>
}
export default Index