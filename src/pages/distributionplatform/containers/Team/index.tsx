import React, { useEffect, useState, useRef } from 'react';
import { Ta<PERSON>, <PERSON><PERSON>, Modal } from 'antd-mobile-v5'
import { Toast } from 'antd-mobile';
import { bridge } from '@sqb/utility';
import { withRouter } from 'react-router-dom';
import Member from './components/Member/index'
import Performance from './components/Performance/index'
import request from 'api/request';
import { API } from 'api/constants';
import fetch from 'api/request/downloadFile';
import { formatProtocol } from "tools/utils/index";
import './index.scss'

const Team = (props) => {
    const columns = [
        {
            label: '成员管理', key: '0'
        }
        // ,
        // {
        //     label: '团队业绩', key: '1'
        // }
    ]
    const stateData: any = [
        {
            label: '待审核', value: 'WAITING_AUDIT'
        },
        {
            label: '已签约', value: 'SIGNED',
        }
    ]
    const teambody = useRef(null);
    const [tabKey, setTabKey] = useState('0')
    const [datas, setData] = useState([])
    const [datestTotal, setDatestTotal] = useState(0)
    const [performanceData, setPerformanceData] = useState([])
    const [performanceDataTotal, setPerformanceDataTotal] = useState(0)
    const [manageData, setManageData] = useState({})
    const [manageIndex, setManageIndex] = useState(1)
    const [performanceIndex, setPerformanceIndex] = useState(1)
    const [placardVisible, setPlacardVisible] = useState<boolean>(false);
    const [placard, setPlacard] = useState<string>('');
    // const [amountData , setAmountData] =useState<Object>({})
    useEffect(() => {
        if (tabKey === '0') {
            memberInit(manageIndex, false)
        }
        if (tabKey === '1') {
            performanceInit(performanceIndex, false)
            // amountTo()
        }
    }, [tabKey])
    useEffect(() => {
        bridge.setTitle('团队管理');
        manageInit()
    }, [])
    const manageInit = () => {
        request.get(API.queryTeamMemberManage).then(res => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            setManageData(data)
        })
    }
    const memberInit = (manageIndex, flag) => {
        const param = { pageNo: manageIndex, pageSize: 10 }
        request.get(API.queryTeamMemberByPage, param).then(res => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            if (!data?.list || !data?.list.length) return;
            if (!flag) {
                setData(datas.length > 0 ? datas : [...datas, data?.list].flat() as any);
            } else {
                setData([...datas, data?.list].flat() as any)

            }
            setDatestTotal(data?.total)
        })
    }
    const performanceInit = (performanceIndex, flag) => {
        const param = { pageNo: performanceIndex, pageSize: 10 }
        request.get(API.queryTeamMemberPerformance, param).then(res => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            if (!data?.list || !data?.list.length) return;
            if (!flag) {
                setPerformanceData(performanceData.length > 0 ?
                    performanceData : [...performanceData, data?.list].flat() as any)
            } else {
                setPerformanceData([...performanceData, data?.list].flat() as any)
            }
            setPerformanceDataTotal(data?.total)

        })
    }
    const viewMore = () => {
        if (tabKey === '0') {
            const sum = manageIndex + 1;
            setManageIndex(sum)
            memberInit(sum, true)
        }
        if (tabKey === '1') {
            const sum = performanceIndex + 1;
            setPerformanceIndex(sum)
            performanceInit(sum, true)
        }
    }
    const exportPerformance = () => {
        fetch.downloadFile(API.downLoadGroupPerformanceExcel, undefined, '团队业绩')
    }
    const tabsAction = (key) => {
        setTabKey(key)
        let str = ''
        columns.map(item => {
            if (item.key === key) {
                str = item.label
            }
            return str
        })
    }
    const addAction = () => {
        props.history.push({ pathname: `/memberconfiguration`, state: { action: 'add' } })

    }
    const shareAction = async () => {
        const res = await request.get(API.getPosterUrlOfInviteUser);
        const { rCode, data, rMsg } = res || {};
        if (rCode !== 0 || !data?.posterUrl) {
            Toast.show(rMsg || '系统错误,请稍后重试');
            return;
        }

        teambody.current.style.overflowY = 'hidden';
        setPlacard(data.posterUrl);
        setPlacardVisible(true);
    }
    const handleClosePlacardModal = () => {
        setPlacardVisible(false);
        teambody.current.style.overflowY = 'auto';
    }
    //  const amountTo =  async () =>{
    //     const res = await request.get(API.queryPerformanceSum);
    //     const { rCode, data, rMsg } = res || {};
    //     if (rCode !== 0) {
    //         Toast.show(rMsg || '系统错误,请稍后重试');
    //         return;
    //     }
    //     setAmountData(data)
    //  }
    return <div className='team-body-all' ref={teambody}>
        <div className='team-body'>
            <div className='team-body-content'>
                <div className='team-body-top'>
                    <div className='team-body-top-title'>
                        <p className='team-body-top-title-left'> {manageData?.channelName}  </p>
                        <p className='team-body-top-title-text' onClick={() => shareAction()}>
                            <span className='team-body-top-right-span' />
                            <span style={{ paddingRight: "8px", paddingTop: "1px" }}> 分享邀约</span>
                        </p>
                    </div>
                    <div className='team-body-top-content'>
                        <div>
                            <p className='team-body-top-content-name'>{manageData?.channelLeader}</p>
                            <p className='team-body-top-content-explain'>渠道负责人</p>
                        </div>
                        <div>
                            <p className='team-body-top-content-num number-font'>{manageData?.memberNumber}</p>
                            <p className='team-body-top-content-explain'>团队成员</p>
                        </div>
                        <div>
                            <p className='team-body-top-content-num number-font'>{manageData?.productNumber}</p>
                            <p className='team-body-top-content-explain'>产品数量</p>
                        </div>
                    </div>
                </div>
                <div className='team-body-top-right'> </div>
                <div className='team-body-tabs'>
                    <div className='team-body-tabs-left'>
                        <Tabs activeLineMode='fixed' activeKey={tabKey} onChange={key => {
                            tabsAction(key)
                        }}>
                            {
                                columns.map(item => (<Tabs.Tab title={item.label} key={item.key} />))
                            }
                        </Tabs>
                    </div>
                </div>
                <div className='team-body-tabs-content'>
                    {tabKey === '0' ? <div>
                        {datas && datas.length > 0 ? <Member stateData={stateData} data={datas} />
                            : datas.length === 0 && <div className='no-data'> 暂无成员 </div>}
                        {
                            datas && (datestTotal <= 10 || datestTotal === datas.length) ? '' :
                                <div className='team-body-tabs-content-view' onClick={() => viewMore()}>
                                    查看更多
                                    <span className='team-body-tabs-content-view-img'>  </span> </div>}
                    </div> :
                        <div>
                            {performanceData && performanceData.length > 0 ?
                                <Performance tableData={performanceData} /> :
                                performanceData.length === 0 && <div className='no-data'> 暂无数据 </div>}
                            {
                                performanceData &&
                                    (performanceDataTotal <= 10 || performanceDataTotal === performanceData.length)
                                    ? '' :
                                    <div className='team-body-tabs-content-view' onClick={() => viewMore()}>
                                        查看更多
                                        <span className='team-body-tabs-content-view-img'>  </span> </div>}
                        </div>}
                </div>
            </div>
        </div>
        {tabKey === '0' ? <div className='team-body-bottom'>
            <Button fill='outline' color='primary' className='team-button'
                onClick={() => addAction()} >
                {/* {tabKey === '0' ? '新增成员' : '导出业绩'}  */}
                新增成员
            </Button>
        </div> : null}
        <Modal
            visible={placardVisible}
            className="placard-modal"
            content={
                <div className="placard-body">
                    <img
                        className="placard-pic"
                        src={formatProtocol(placard)}
                        alt="海报"
                    />
                    <p className="modal-tip">长按保存或分享海报</p>
                    <i
                        className="icon-close"
                        onClick={() => handleClosePlacardModal()}
                    ></i>
                </div>
            }
        >
        </Modal>
    </div>;

};
export default withRouter(Team);