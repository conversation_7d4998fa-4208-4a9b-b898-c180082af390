import React from "react";
// import { getRequestParams } from 'tools/utils/index';
const Home = React.lazy(() => import('./Home/index' /* webpackChunkName:"StartPager" */));
const Team = React.lazy(() => import('./Team/index' /* webpackChunkName:"AgentExam" */));
const My = React.lazy(() => import('./My/index' /* webpackChunkName:"My" */));
const SigningApplication = React.lazy(() => import('./SigningApplication/index' /* webpackChunkName:"SigningApplication" */));
const MemberConfiguration = React.lazy(() => import('./MemberConfiguration/index' /* webpackChunkName:"MemberConfiguration" */));
const Sign = React.lazy(() => import('./Sign/index' /* webpackChunkName:"Sign" */));
const Unsigned = React.lazy(() => import('./Unsigned/index' /* webpackChunkName:"Unsigned" */));
const Index = React.lazy(() => import('./Index/index' /* webpackChunkName:"Index" */));
const Channel = React.lazy(() => import('./Channel/index' /* webpackChunkName:"Channel" */));
const TeamConfiguration = React.lazy(() => import('./TeamConfiguration/index' /* webpackChunkName:"TeamConfiguration" */));
const QRcodeToProductDetail = React.lazy(() => import("./QRcodeToProductDetail/index" /* webpackChunkName:"QRcodeToProductDetail" */));
const AdditionalRemarks = React.lazy(() => import("./AdditionalRemarks/index" /* webpackChunkName:"AdditionalRemarks" */));
const Login = React.lazy(() => import("./Login/index" /* webpackChunkName:"Login" */));

const routers = [
    {
        path: '/home',   // 首页
        level: 1,
        component: Home,
    },
    {
        path: '/team',  // 团队管理
        level: 1,
        component: Team,
    },
    {
        path: '/my',    // 我的
        level: 1,
        component: My,
    },
    {
        path: '/signingapplication/:leaderOrgId/:leaderOrgType', // 签约申请
        component: SigningApplication,
    },
    {
        path: '/memberconfiguration',  //成员配置
        component: MemberConfiguration,
    },
    {
        path: '/sign',          // 审核中
        component: Sign,
    },
    {
        path: '/unsigned',
        component: Unsigned,
    },
    {
        path: '/index/:leaderOrgId?/:leaderOrgType?',
        component: Index,
    },
    {
        path: '/channel', // 渠道管理
        level: 1,
        component: Channel,
    },
    {
        path: '/teamconfiguration', // 团队配置
        component: TeamConfiguration,
    },
    {
        path: '/QRcodeToProductDetail', // 贷款扫二维码中间页
        component: QRcodeToProductDetail,
    },
    {
        path: "/AdditionalRemarks/:id",//补充说明
        component: AdditionalRemarks,
    },
    {
        path: '/Login', // 登录
        component: Login,
    }
]

export default routers;

