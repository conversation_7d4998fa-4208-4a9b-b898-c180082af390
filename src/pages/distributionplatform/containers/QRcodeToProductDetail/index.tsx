import React, { useEffect } from "react";
import { useLocation } from "react-router";
import request from "api/request";
import API from "api/const";
// import { Toast } from "antd-mobile";
import "./index.scss";
import { bridge } from "@sqb/utility";

const QRcodeToProductDetail = () => {
    const location = useLocation();

    // 校验贷款海报的token
    const getCheckLoanUrl = async () => {
        const searchParams = new URLSearchParams(location.search);
        const paramUuid = searchParams.get("uuid")
            ? searchParams.get("uuid")
            : "";
        const paramImage = searchParams.get("image")
            ? searchParams.get("image")
            : "";

        console.log(paramUuid, "--paramUuid");
        request
            .get(API.GET_CHECK_LOAN_URL, {
                uuid: paramUuid,
                image: paramImage,
            })
            .then((res) => {
                const { data } = res || {};
                if (data.valid) {
                    window.location.replace(data.posterUrl);
                    // window.location.replace("https://www.baidu.com");
                    // window.location.replace("weixin://dl/business/?appid=wx20a578b820f04c9c&path=pages/addPlanner/addPlanner");
                } else {
                    // Toast.info(data.message || "系统错误,请稍后重试");
                    alert(data.message || "系统错误,请稍后重试");
                }
            });
    };

    useEffect(() => {
        bridge.setTitle("营销平台");
        getCheckLoanUrl();
    }, []);

    return <div></div>;
};
export default QRcodeToProductDetail;
