import React, { useEffect } from "react";
import { useHistory } from "react-router-dom";
import { useParams ,useLocation} from "react-router";
import { Toast } from "antd-mobile-v5";
import { bridge } from '@sqb/utility';
import request from 'api/request';
import { API } from 'api/constants';
import Loading from 'src/components_common/Loading';

import { statusUrlMap } from './constants';

const Index = () => {
    const history = useHistory();
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const leaderSaleId = searchParams.get("leaderSaleId") ? searchParams.get("leaderSaleId") : "";
    
    const { leaderOrgId, leaderOrgType } = useParams<{ leaderOrgId: string; leaderOrgType: string; }>();

    // 页面初始化
    const init = async () => {
        let params = {
            leaderOrgId: leaderOrgId,
            leaderOrgType: leaderOrgType
        }
        if (leaderOrgId === undefined) {
            delete params.leaderOrgId
        }
        if (leaderOrgType === undefined) {
            delete params.leaderOrgType
        }
        if (leaderOrgId) {
            // 说明此时是从公众号进入的 不是扫码进来的
            const res = await request.get(API.getRegisterCheck, { orgId: leaderOrgId,leaderSaleId:leaderSaleId });
            console.log(res, "orgId");
            if (res.rCode === -1) {
                return Toast.show(res.rMsg)
            }

        }

        request.get(API.getSalesInfo, { ...params }).then(res => {
            const { rCode, data } = res || {};
            if (rCode !== 0) return;

            const { salesStatus } = data || {};
            let url = statusUrlMap[salesStatus];
            if (salesStatus === 'NO_SIGNED') {
                if (!leaderOrgId || !leaderOrgType) {
                    Toast.show({
                        content: '页面链接错误，请确认！',
                    });
                    return;
                }

                url += `/${leaderOrgId}/${leaderOrgType}?leaderSaleId=${leaderSaleId}`;
                console.log(url,"---url")
                console.log(leaderSaleId,"---leaderSaleId")
            }

            history.replace(url);
        });
    };

    useEffect(() => {
        bridge.setTitle('营销平台');
        init();
    }, [])

    return (
        <Loading />
    );
};

export default Index;
