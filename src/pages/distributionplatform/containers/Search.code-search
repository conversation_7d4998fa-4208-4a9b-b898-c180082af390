# Query: 分销平
# ContextLines: 1

32 个结果 - 11 文件

public/distributionplatform.html:
  10  
  11:     <title>营销平台</title>
  12      <style>

src/setupProxy.js:
  116  
  117:      // 分销平台
  118       app.use(

src/pages/distributionplatform/components/Bottom/constants.ts:
  3          event_id: 18,
  4:         eventName: '分销平台-首页/渠道管理/团队管理/我的业绩',
  5      },

src/pages/distributionplatform/containers/Channel/constants.ts:
   3          event_id: 19,
   4:         eventName: '分销平台-渠道管理pv/uv',
   5      },

   7          event_id: 71,
   8:         eventName: '分销平台-渠道管理-成员配置页面曝光',
   9      },

  11          event_id: 24,
  12:         eventName: '分销平台-渠道管理-成员配置页面-确认点击按钮',
  13      },

  15          event_id: 23,
  16:         eventName: '分销平台-渠道管理-新增团队按钮点击',
  17      },

  19          event_id: 22,
  20:         eventName: '分销平台-渠道管理-导出业绩按钮点击',
  21      },

  23          event_id: 21,
  24:         eventName: '分销平台-渠道管理-成员管理/业绩查询切换',
  25      },

  27          event_id: 20,
  28:         eventName: '分销平台-渠道管理-分享按钮点击',
  29      }

src/pages/distributionplatform/containers/Home/constants.ts:
   3          event_id: 11,
   4:         eventName: '分销平台-首页 PV/UV',
   5      },

   7          event_id: 12,
   8:         eventName: '分销平台-首页 点击产品列表',
   9      },

  11          event_id: 13,
  12:         eventName: '分销平台-首页 下载海报',
  13          prductName:'上报下载海报'

  16          event_id: 14,
  17:         eventName: '分销平台-首页 复制链接按钮点击量',
  18          prductName:'上报复制链接'

  21          event_id: 15,
  22:         eventName: '分销平台-首页 产品列表一级分类pv',
  23      },

  25          event_id: 16,
  26:         eventName: '分销平台-首页 产品列表一级分类险种点击',
  27          prductName:'上报点击险种名称'

  30          event_id: 17,
  31:         eventName: '分销平台-首页 产品详情点击量',
  32          prductName:'上报产品名称'

src/pages/distributionplatform/containers/Index/index.tsx:
  49      useEffect(() => {
  50:         bridge.setTitle('营销平台');
  51          init();

src/pages/distributionplatform/containers/MemberConfiguration/constants.ts:
  3          event_id: 70,
  4:         eventName: '分销平台-团队管理-成员配置页面曝光',
  5      },

  7          event_id: 10,
  8:         eventName: '分销平台-团队管理-成员配置页面-确认点击按钮',
  9      }

src/pages/distributionplatform/containers/My/constants.ts:
   3          event_id: 1,
   4:         eventName: '分销平台-我的业绩 PV/UV',
   5      },

   7          event_id: 2,
   8:         eventName: '分销平台-我的业绩-数据切换',
   9          type:'当周业绩上周业绩'

  12          event_id: 3,
  13:         eventName: '分销平台-我的业绩-复制按钮点击',
  14          productName:'上报具体产品名称'

  17          event_id: 4,
  18:         eventName: '分销平台-我的业绩-导出业绩按钮点击',
  19          type:'当周业绩上周业绩'

src/pages/distributionplatform/containers/Team/constants.ts:
   3          event_id: 5,
   4:         eventName: '分销平台-团队管理pv/uv',
   5      },

   7          event_id: 9,
   8:         eventName: '分销平台-团队管理-新增成员按钮点击',
   9      },

  11          event_id: 8,
  12:         eventName: '分销平台-团队管理-导出业绩按钮点击',
  13      },

  15          event_id: 7,
  16:         eventName: '分销平台-团队管理-成员管理/业绩查询切换',
  17      },

  19          event_id: 6,
  20:         eventName: '分销平台-团队管理-分享按钮点击',
  21      }

src/pages/distributionplatform/containers/TeamConfiguration/constants.ts:
  3          event_id: 71,
  4:         eventName: '分销平台-渠道管理-团队配置页面曝光',
  5      },

  7          event_id: 24,
  8:         eventName: '分销平台-渠道管理-团队配置页面-确认点击按钮',
  9      },

src/pages/distributionplatform/containers/Unsigned/index.tsx:
  8      useEffect(() => {
  9:         bridge.setTitle('营销平台');
  10      }, [])
