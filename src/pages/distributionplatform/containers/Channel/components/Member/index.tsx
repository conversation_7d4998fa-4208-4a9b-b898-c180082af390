import React, { useEffect, useState, useRef } from 'react';
import { Form, Input, Button, Radio, Popup } from 'antd-mobile-v5'
import { withRouter } from 'react-router-dom';
import './index.scss'


const Index  = (props:any) =>{
    const { data ,stateData } = props
    const pickerMethod = (value) => {
        let str = ''
        stateData.forEach(item => {
                if (item.value === value) {
                    str = item.label
                }
        })
        return str
    }
    return <div className='member-body'>
        {
          data.map((item,index) =>{
                return  <>
                       <Form.Item  onClick={()=> props.history.push({pathname:'/teamconfiguration',state:{action:'edit',id:item.id}})} >
                        <div className='member-body-left' key={index}>
                             <p className='member-body-left-name'> {item?.orgName}</p> 
                              </div>
                        <div className={item?.saleStatus === 'WAITING_AUDIT' ? 'member-body-rigth1' : 'member-body-rigth2'}>  {pickerMethod(item?.saleStatus)} </div>
                      </Form.Item>
                </> 
            })
        }
    </div>
}
export default withRouter(Index)