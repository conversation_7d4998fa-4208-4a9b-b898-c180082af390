.team-body-all {
    @import "~scss/index";
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    overflow-y: auto;
    .team-body {
        flex: 1;
        overflow-y: auto;
        .team-body-content {
            position: relative;
        }

        .team-body-top-right {
            position: absolute;
            right: 20px;
            top: 8px;
            width: 108px;
            height: 35.5px;
            background-color: rgba(0, 198, 130, 1);
            border-radius: 8px;
            display: flex;
            justify-content: end;
            span {
                display: inline-block;
            }
        }

        .team-body-top {
            position: relative;
            border-radius: 8px;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin: 16px 12px 20px;
            background-image: url("#{$wos-cdn}qip5ume0__w1404_h576.png");
            // height: 144px;
            // height: 100%;
            z-index: 1;
            .team-body-top-title {
                display: flex;
                justify-content: space-between;
                color: rgba(51, 51, 51, 1);
                padding: 10px 12px 24px 12px;

                .team-body-top-title-left {
                    font-family: PingFangSC-Semibold;
                    font-size: 20px;
                    font-weight: 700;
                    margin-top: 4px;
                }

                .team-body-top-title-text {
                    font-family: PingFangSC-Semibold;
                    font-size: 12px;
                    font-weight: 700;
                    color: rgba(255, 255, 255, 1);
                    display: flex;
                    padding-top: 4px;
                    span {
                        display: block;
                    }

                    .team-body-top-right-span {
                        width: 16px;
                        height: 16px;
                        background-repeat: no-repeat;
                        background-size: 100%;
                        background-image: url("#{$wos-cdn}50bkrgnm__w64_h64.png");
                        margin-left: 4px;
                        padding-top: 1px;
                    }
                }
            }

            .team-body-top-content {
                height: 51px;
                display: flex;
                padding: 10px 0 23.5px 0;
                align-items: center;

                div {
                    flex: 1;
                    // text-align: center;
                    padding-left: 12px;

                    .team-body-top-content-name {
                        font-family: PingFangTC-Semibold;
                        font-size: 20px;
                        font-weight: 700;
                        color: rgba(51, 51, 51, 1);
                        padding-bottom: 8px;
                        padding-top: 2px;
                    }

                    .team-body-top-content-num {
                        font-size: 26px;
                        font-weight: 700;
                        color: rgba(51, 51, 51, 1);
                        padding-bottom: 1px;
                    }

                    .team-body-top-content-explain {
                        font-family: PingFangSC-Regular;
                        font-size: 12px;
                        font-weight: 400;
                        color: rgba(102, 102, 102, 1);
                    }
                }
            }
        }

        .team-body-tabs {
            padding: 0 12px 8px 4px;
            display: flex;
            justify-content: space-between;
            position: sticky;
            top: 0;
            background-color: #fff;
            z-index: 1;
            align-items: center;
            height: 48px;

            .team-body-tabs-left {
                .adm-tabs-header {
                    border: none;
                }

                .adm-tabs-tab {
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(102, 102, 102, 1);
                }

                .adm-tabs-tab-active {
                    font-family: PingFangSC-Semibold;
                    font-size: 18px;
                    font-weight: 700;
                    color: rgba(51, 51, 51, 1);
                }

                .adm-tabs-tab-line {
                    width: 21px !important;
                    height: 5px !important;
                    background: url("#{$wos-cdn}amvdre58__w85_h20.png") no-repeat;
                    background-size: cover;
                    bottom: 1px;
                }
            }

            .team-body-tabs-right {
                height: 28px;
                background-color: rgba(0, 198, 130, 1);
                border-radius: 14px;
                line-height: 28px;

                .team-button {
                    font-family: PingFangSC-Semibold;
                    font-size: 12px;
                    font-weight: 700;
                    color: rgba(255, 255, 255, 1);
                    border: none;
                }
            }
        }

        .team-body-tabs-content {
            margin: 0 0 12px 0;
            border-radius: 8px;
            background: rgba(255, 255, 255, 1);

            .no-data {
                height: 94px;
                background-color: rgba(255, 255, 255, 1);
                border-radius: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                font-weight: 400;
                color: rgba(51, 51, 51, 1);
            }

            .adm-swiper {
                height: 100%;
            }

            .team-body-tabs-content-view {
                width: 100%;
                height: 38px;
                text-align: center;
                line-height: 38px;
                font-family: PingFangSC-Regular;
                font-size: 14px;
                font-weight: 400;
                color: rgba(153, 153, 153, 1);
            }

            .team-body-tabs-content-view-img {
                display: inline-block;
                margin-left: 4px;
                width: 8px;
                height: 8px;
                background-image: url("#{$wos-cdn}es6v6ku7__w32_h32.png");
                background-size: cover;
            }
        }
    }

    .team-body-bottom {
        width: 100%;
        height: 64px;
        line-height: 64px;
        text-align: center;
        // position: fixed;
        // bottom: 46px;
        background-color: #fff;

        button {
            width: 343px;
            height: 48px;
            border: 1px solid #00c682;
            text-align: center;
            border-radius: 36px;
            font-family: PingFangSC-Medium;
            font-size: 17px;
            font-weight: 500;
            color: rgba(0, 198, 130, 1);
        }
    }
    .placard-modal {
        width: 100%;
        height: 100%;
        z-index: 3;
        .adm-center-popup-body{
            --background-color: transparent;
        }
        .adm-center-popup-wrap {
            background-color: transparent;
        }
        .adm-modal-body,
        .adm-modal-content {
            max-height: 80vh;
            height: 80vh;
            padding: 0;
            border-radius: 0;
        }

        .placard-body {
            width: 100%;
            height: 100%;
            .adm-center-popup-body {
                --background-color: transparent;
            }
        }

        .placard-pic {
            display: block;
            max-width: 100%;
            max-height: calc(100% - 80px);
            margin: 0 auto;
        }

        .modal-tip {
            font-size: 14px;
            font-weight: 700;
            line-height: 20px;
            color: rgba(255, 255, 255, 1);
            text-align: center;
            margin: 8px 0 24px 0;
        }

        .icon-close {
            display: block;
            width: 28px;
            height: 28px;
            margin: 0 auto;
            background: transparent url("#{$wos-cdn}rfgtv73c__w112_h112.png") no-repeat;
            background-size: cover;
        }
    }
}
