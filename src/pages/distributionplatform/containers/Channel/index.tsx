import React, { useEffect, useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button ,Modal } from 'antd-mobile-v5'
import { Toast } from 'antd-mobile';
import { bridge } from '@sqb/utility';
import { reportPoint } from "tools/reportPoint";
import { SwiperRef } from 'antd-mobile-v5/es/components/swiper'
import { wmdaDataHome } from './constants';
import { withRouter } from 'react-router-dom';
import Member from './components/Member/index'
import Performance from './components/Performance/index'
import request from 'api/request';
import { API } from 'api/constants';
import { copyText } from 'tools/utils/libs/copyText';
import fetch from 'api/request/downloadFile';
import { formatProtocol } from "tools/utils/index";
import './index.scss'

const Channel = (props) => {
    const columns = [
        {
            label: '团队管理', key: '0'
        }
        // ,
        // {
        //     label: '团队业绩', key: '1'
        // }
    ]
    const stateData: any = [
        {
            label: '待审核', value: 'WAITING_AUDIT'
        },
        {
            label: '已签约', value: 'SIGNED',
        },
        {
            label: '已解约', value: 'TERMINATED',
        },
    ]
    const swiperRef = useRef<SwiperRef>(null)
    const teambody = useRef(null);
    const [tabKey, setTabKey] = useState('0')
    const [datas, setData] = useState([])
    const [datestTotal, setDatestTotal] = useState(0)
    const [performanceData, setPerformanceData] = useState([])
    const [performanceDataTotal, setPerformanceDataTotal] = useState(0)
    const [manageData, setManageData] = useState({})
    const [manageIndex, setManageIndex] = useState(1)
    const [performanceIndex, setPerformanceIndex] = useState(1)
    const [placardVisible, setPlacardVisible] = useState<boolean>(false);
    const [placard, setPlacard] = useState<string>('');
    // const [amountData , setAmountData] =useState<Object>({})
    useEffect(() => {
        if (tabKey === '0') {
            memberInit(manageIndex, false)
        }
        if (tabKey === '1') {
            performanceInit(performanceIndex, false)
            // amountTo()
        }
    }, [tabKey])
    useEffect(() => {
        bridge.setTitle('渠道管理');
        manageInit()
        reportPoint(wmdaDataHome.pageShow);
    }, [])
    const manageInit = () => {
        request.get(API.queryChannelManage).then(res => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            setManageData(data)
        })
    }
    const memberInit = (manageIndex, flag) => {
        const param = { pageNo: manageIndex, pageSize: 10 }
        request.get(API.queryTeamByPage, param).then(res => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            if (!data?.list || !data?.list.length) return;
            if (!flag) {
                setData(datas.length > 0 ? datas : [...datas, data?.list].flat() as any);
            } else {
                setData([...datas, data?.list].flat() as any)

            }
            setDatestTotal(data?.total)
        })
    }
    const performanceInit = (performanceIndex, flag) => {
        const param = { pageNo: performanceIndex, pageSize: 10 }
        request.get(API.queryTeamPerformance, param).then(res => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            if (!data?.list || !data?.list.length) return;
            if (!flag) {
                setPerformanceData(performanceData.length > 0 ?
                    performanceData : [...performanceData, data?.list].flat() as any)
            } else {
                setPerformanceData([...performanceData, data?.list].flat() as any)
            }
            setPerformanceDataTotal(data?.total)

        })
    }
    const viewMore = () => {
        if (tabKey === '0') {
            const sum = manageIndex + 1;
            setManageIndex(sum)
            memberInit(sum, true)
        }
        if (tabKey === '1') {
            const sum = performanceIndex + 1;
            setPerformanceIndex(sum)
            performanceInit(sum, true)
        }
    }
    const exportPerformance = () => {
        fetch.downloadFile(API.downLoadTeamPerformanceExcel, undefined, '团队业绩')
    }
    const tabsAction = (key) => {
        setTabKey(key)
        swiperRef.current?.swipeTo(Number(key))
        let str = ''
        columns.map(item => {
            if (item.key === key) {
                str = item.label
            }
            return str
        })
        reportPoint({ ...wmdaDataHome.pageShowType, type: str });
    }
    const addAction = () => {
        // if (tabKey === '0') {
            props.history.push({ pathname: `/teamConfiguration`, state: { action: 'add' } })
            reportPoint(wmdaDataHome.pageShowAdd);
        // } 
        // else {
        //     exportPerformance()
        //     reportPoint(wmdaDataHome.pageShowExport);
        // }
    }
    const shareAction = async () => {
    const res = await request.get(API.getPosterUrlOfInviteUser);
    const { rCode, data, rMsg } = res || {};
    if (rCode !== 0 || !data?.posterUrl) {
        Toast.show(rMsg || '系统错误,请稍后重试');
        return;
    }

    teambody.current.style.overflowY= 'hidden';
    setPlacard(data.posterUrl);
    setPlacardVisible(true);
    reportPoint({ ...wmdaDataHome.pageShowShare, name: manageData?.channelLeader, channeName: manageData?.channelName });
}
const handleClosePlacardModal = () => {
    setPlacardVisible(false);
    teambody.current.style.overflowY= 'auto';
}
    // const amountTo =  async () =>{
    //     const res = await request.get(API.queryPerformanceSum);
    //     const { rCode, data, rMsg } = res || {};
    //     if (rCode !== 0) {
    //         Toast.show(rMsg || '系统错误,请稍后重试');
    //         return;
    //     }
    //     setAmountData(data)
    //  }
    return  <div className='team-body-all'  ref={teambody}> 
    <div className='team-body'>
        <div className='team-body-content'>
            <div className='team-body-top'>
                    <div className='team-body-top-title'>
                        <p className='team-body-top-title-left'> {manageData?.channelName}  </p>
                        <p className='team-body-top-title-text'  onClick={() => shareAction()}>
                            <span className='team-body-top-right-span' />
                            <span style={{paddingRight:"8px"  ,paddingTop:"1px"}}> 分享邀约</span>
                        </p>
                    </div>
                    <div className='team-body-top-content'>
                        <div>
                            <p className='team-body-top-content-name'>{manageData?.channelLeader}</p>
                            <p className='team-body-top-content-explain'>渠道负责人</p>
                        </div>
                        {/* <div>
                            <p className='team-body-top-content-num number-font'>{manageData?.teamNumber}</p>
                            <p className='team-body-top-content-explain'>团队数量(个)</p>
                        </div> */}
                        <div>
                            <p className='team-body-top-content-num number-font'>{manageData?.channelProductNumber}</p>
                            <p className='team-body-top-content-explain'>产品数量(款)</p>
                        </div>
                    </div>
                </div>
                <div className='team-body-top-right'> </div>
            <div className='team-body-tabs'>
                <div className='team-body-tabs-left'>
                    <Tabs activeLineMode='fixed' activeKey={tabKey} onChange={key => {
                        tabsAction(key)
                    }}>
                        {
                            columns.map((item, index) => (<Tabs.Tab title={item.label} key={item.key} />))
                        }
                    </Tabs>
                </div>
            </div>
            <div className='team-body-tabs-content'>
                {tabKey === '0' ? <div>
                    {datas && datas.length > 0 ? <Member stateData={stateData} data={datas} />
                        : datas.length === 0 && <div className='no-data'> 暂无团队 </div>}
                    {datas && (datestTotal <= 10 || datestTotal === datas.length) ? <></>
                        : <div className='team-body-tabs-content-view' onClick={viewMore}>
                            查看更多
                            <span className='team-body-tabs-content-view-img'>  </span> </div>}
                </div> :
                    <div>
                        {performanceData && performanceData.length > 0
                            ? <Performance tableData={performanceData}/>
                            : performanceData.length === 0 && <div className='no-data'> 暂无数据 </div>}
                        {performanceData &&
                            (performanceDataTotal <= 10 || performanceDataTotal === performanceData.length)
                            ? <></>
                            : <div className='team-body-tabs-content-view' onClick={viewMore}>
                                查看更多
                                <span className='team-body-tabs-content-view-img'>  </span> </div>}
                    </div>}
            </div>
        </div>
    </div>
    { tabKey === '0' ? <div className='team-body-bottom'>
                    <Button fill='outline' color='primary' className='team-button'
                        onClick={() => addAction()} >
                        {/* {tabKey === '0' ? '新增成员' : '导出业绩'}  */}
                        新增团队
                        </Button>
                </div> : null}
                <Modal
                visible={placardVisible}
                className="placard-modal"
                content={
                    <div className="placard-body">
                        <img
                            className="placard-pic"
                            src={formatProtocol(placard)}
                            alt="海报"
                        />
                        <p className="modal-tip">长按保存或分享海报</p>
                        <i
                            className="icon-close"
                            onClick={() => handleClosePlacardModal()}
                        ></i>
                    </div>
                }
            >
            </Modal>
    </div>;

};
export default withRouter(Channel);