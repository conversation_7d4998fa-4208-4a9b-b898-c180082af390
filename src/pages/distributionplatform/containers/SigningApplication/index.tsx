import React, { useEffect, useState } from 'react';
import { Form, Input, Button } from 'antd-mobile-v5'
import { bridge } from '@sqb/utility';
import { useHistory } from "react-router-dom";
import { useParams,useLocation } from "react-router";
import { Toast } from 'antd-mobile';
import request from 'api/request';
import { API } from 'api/constants';
import rules from 'api/rules'
import "./index.scss"

const SigningApplication = (props) => {
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const leaderSaleId = searchParams.get("leaderSaleId") ? searchParams.get("leaderSaleId") : "";
    const [orgName, setOrgName] = useState('')
    const [form] = Form.useForm()
    const history = useHistory();
    const { leaderOrgId, leaderOrgType } = useParams<{ leaderOrgId: string; leaderOrgType: string; }>();
    useEffect(() => {
        bridge.setTitle('签约申请');
        init()
    }, [])
    const init = () => {
        request.get(API.getSalesInfo, { leaderOrgId, leaderOrgType }).then(res => {
            const { rCode, data, rMsg } = res || {};
            if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
            form.setFieldsValue(data)
            setOrgName(data?.orgName)
        });
    }
    const onSubmit = () => {
        form.validateFields().then((values) => {
            let params = {
                ...values,
                leaderOrgId,
                leaderOrgType,
                leaderSaleId
            }
            request.post(API.register, params).then(res => {
                const { rCode, rMsg } = res || {};
                if (rCode !== 0) return Toast.info(rMsg || '系统错误,请稍后重试');
                props.history.replace('/sign')
            });
        })
            .catch((err) => {
                console.log(err, 'rrrr')
            })
    }
    return <div className='signing-body'>
        <div className='signing-body-header-center'>
            <div className='signing-body-header-form-top'>
                <Form form={form} layout='horizontal' >
                    <Form.Header> 基本信息 </Form.Header>
                    <Form.Item label='姓名' name='saleName'
                        className='required'
                        rules={[{ 
                            validator: (rule, value, callback) => {
                                if(!value){
                                    callback('请输入姓名')
                                }else if(/\s/.test(value)){
                                        callback('请输入合法姓名')
                                }else{
                                    callback()
                                }
                            }
                         }]}
                        >
                        <Input placeholder='请输入'  style={{ color: '#33333' }}/>
                    </Form.Item>
                    {/* <Form.Item label='身份证号' name='idCode' 
                     className='required'
                    rules={[{required:true ,  validator(rule, value, callback) {
                        console.log(Rules.isIdCard(value.idCode) , 'ssssss')
                        if(!value){
                            callback('请输入身份证号')
                        }else if(!Rules.isIdCard(value.idCode)){
                            callback('身份证格式错误')
                        }else{
                            callback()
                        }
                    }}]} 
                    style={{color:'#33333'}}>
                        <Input placeholder='请输入' />
                    </Form.Item> */}
                    <Form.Item label='手机号' name='phone' 
                        rules={[
                            {
                                validator: (rule, value, callback) => {
                                    if (!value) {
                                        callback('请输入手机号')
                                    } else if (!rules.isMobilePhone(value)) {
                                        callback('手机号格式不正确')
                                    } else {
                                        callback()
                                    }
                                }
                            }
                        ]}
                    >
                        <Input type='tel' placeholder='请输入' readOnly style={{ color: '#999999' }} />
                    </Form.Item>
                </Form>
            </div>
            <div className='signing-body-header-form-bottom'>
                <Form form={form} layout='horizontal'>
                    <Form.Header> 渠道信息 </Form.Header>
                    <Form.Item label='渠道名称' name='orgName' 
                    rules={[{ 
                        validator: (rule, value, callback) => {
                           if(/\s/.test(value)){
                                    callback('请输入合法渠道名称')
                            }else{
                                callback()
                            }
                        }
                     }]}
                   
                    >
                        <Input placeholder='请输入' readOnly={!!orgName}   style={{ color: orgName ? '#999999' : '#33333' }}/>
                    </Form.Item>
                    <Form.Item label='上级联系人' name='leaderName' 
                        rules={[{ required: true, message: '请输入上级联系人' }]}
                    >
                        <Input placeholder='请输入' readOnly style={{ color: '#999999' }} />
                    </Form.Item>
                </Form>
            </div>
        </div>
        <div className='signing-body-bottom-button'>
            <Button block color='primary' size='large' onClick={() => { onSubmit() }} > 提交 </Button>
        </div>
    </div>;

};
export default SigningApplication;
