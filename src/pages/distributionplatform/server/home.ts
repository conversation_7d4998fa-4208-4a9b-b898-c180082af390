import { isProduction } from "@sqb/utility";
import request from "api/request";
const EMS = `//emsapi.${isProduction ? "58" : "58v5"}.${
    isProduction ? "com" : "cn"
}/api/ems/get/data`;
console.log(EMS, "EMS");
console.log(isProduction, "isProduction");
// ========================是否需要授权========================
export interface IProtocolItem {
    key: string;
    name: string;
    isUsing: boolean;
    protocolList: string[];
}

//获取授权协议配置列表
export const getLoginUserLearning = async (): Promise<IProtocolItem[]> => {
    const requestId = "20250108000679";
    const res = await request.jsonp(`${EMS}?id=${requestId}`);
    return res.list[requestId];
};
//获取国民信托的用户协议
export const getNationalTrustLoginUserLearning = async (): Promise<
    IProtocolItem[]
> => {
    const requestId = "20250108000680";
    const res = await request.jsonp(`${EMS}?id=${requestId}`);
    return res.list[requestId];
};
