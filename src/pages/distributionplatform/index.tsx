import 'whatwg-fetch'
import 'scss_mixin/reset.scss' //reset 样式
import 'tools/polyfill'
import React from 'react'
import ReactDOM from 'react-dom'
import { BrowserRouter as Router } from "react-router-dom";
import App from './containers/App';
import './index.scss';

ReactDOM.render(
    <React.Suspense fallback={<div></div>}>
        <Router basename="/agentapp/distributionplatform">
            <App/>
        </Router>
    </React.Suspense>,

    document.getElementById('root')
);



