/**
 * 一些常用的校验规则
 */

// 身份证号校验
let isIdCard = value => {
	const aCity = {
		11: "北京",
		12: "天津",
		13: "河北",
		14: "山西",
		15: "内蒙古",
		21: "辽宁",
		22: "吉林",
		23: "黑龙江 ",
		31: "上海",
		32: "江苏",
		33: "浙江",
		34: "安徽",
		35: "福建",
		36: "江西",
		37: "山东",
		41: "河南",
		42: "湖北 ",
		43: "湖南",
		44: "广东",
		45: "广西",
		46: "海南",
		50: "重庆",
		51: "四川",
		52: "贵州",
		53: "云南",
		54: "西藏 ",
		61: "陕西",
		62: "甘肃",
		63: "青海",
		64: "宁夏",
		65: "新疆",
		71: "台湾",
		81: "香港",
		82: "澳门",
		91: "国外 "
	};
	let iSum = 0;
	if (!value) {
		return false;
	}
	if (!(value.length === 15 || value.length === 18)) {
		return false;
	}
	if (!/^\d{15}|\d{17}(\d|x)$/i.test(value)) {
		return false;
	}
	value = value.replace(/x$/i, "a");
	if (aCity[parseInt(value.substr(0, 2),10)] === undefined) {
		return false;
	}
	let sBirthday = value.substr(6, 4) + "/" + Number(value.substr(10, 2)) + "/" + Number(value.substr(12, 2));
	let d = new Date(sBirthday);
	if (sBirthday !== (d.getFullYear() + "/" + (d.getMonth() + 1) + "/" + d.getDate())) {
		return false;
	}
	for (let i = 17; i >= 0; i--) {
		iSum += (Math.pow(2, i) % 11) * parseInt(value.charAt(17 - i), 11);
	}
	if (iSum % 11 !== 1) {
		return false;
	}
	return true;
};
/**
 * 银行卡校验
 * @param {String} value 银行卡号
 * @return {Boolean}
 */
let validateCardLuhn = cardNumber => {
	// 卡号字符串化并去除空格，仅保留数字
	let orign_num = cardNumber + ''; // 字符串化
	let str_digits = orign_num.replace(/[\D]/g, '');
	// 业务不让用户输空格，做下判断
	if (orign_num.length > str_digits.length) {
		return false;
	}
	// 银行卡号必须为12-19位数字
	if (!/^\d{12,19}$/.test(str_digits)) {
		return false;
	}
	return true; //效验规则过期先去掉
	/**
    // 根据luhn规则，将卡号数组化，并反转顺序，以便于操作
    let luhn_digits = str_digits.split('').reverse();
    // 取第1位作为后续的验证号码
    let luhn_checkcode = parseInt(luhn_digits.shift(), 10);

    let loop_length = luhn_digits.length;
    let loop_index = loop_length;

    let luhn_sum = 0;
    for (; loop_index > 0; loop_index--) {
        let _i = loop_length - loop_index;
        let _k = parseInt(luhn_digits[_i], 10);
        let _add_val = _k;
        // 偶数字段 需要*2，并且大于10的数字要相加2个位数的值
        if ((_i % 2) === 0) {
            let _k2 = _k * 2;
            switch (_k2) {
                case 10:
                    _add_val = 1;
                    break;
                case 12:
                    _add_val = 3;
                    break;
                case 14:
                    _add_val = 5;
                    break;
                case 16:
                    _add_val = 7;
                    break;
                case 18:
                    _add_val = 9;
                    break;
                default:
                    _add_val = _k2;
            }
        }
        luhn_sum += _add_val;
    }

    /* 方法1
        1. 从校验位开始，从右往左，偶数位乘2，然后将两位数字的个位与十位相加；
        2. 计算所有数字的和（67）；
        3. 乘以9（603）；
        4. 取其个位数字（3），得到校验位。
        */
	/**let luhn_sum9 = luhn_sum * 9;
    let luhn_sum9_last_code = parseInt((luhn_sum9 + '').replace(/\d+(\d$)/, '$1'), 10);
    return (luhn_sum9_last_code === luhn_checkcode);

    /* 方法2
        1. 从校验位(即不包括该位数)开始，从右往左，偶数位乘2（例如，7*2=14），然后将两位数字的个位与十位相加（例如，10：1+0=1）；
        2. 把得到的数字加在一起；
        3. 将数字的和取模10（本例中得到7），再用10去减（本例中得到3），得到校验位。
        */
	// var luhn_sum_mod10 = luhn_sum%10,
	//     luhn_sum_checkcode = 10 - luhn_sum_mod10;
	// return (luhn_sum_checkcode===luhn_checkcode);

	/* 方法3
        1. 从校验位(即不包括该位数)开始，从右往左，偶数位乘2（例如，7*2=14），然后将两位数字的个位与十位相加（例如，10：1+0=1）；
        2. 把得到的数字加在一起；
        3. 再加上检验位的数值，将结果取模10，如果余数为0，则符合规则。
        */
	// return (((luhn_sum+luhn_checkcode)%10) === 0);
};

const ONE_YEAR_OLD = 10000;
const EIGHTEEN_YEAR_OLD = 18 * ONE_YEAR_OLD;
const ADDRESS_LEV1 = "乡镇街路道巷委段里社园苑";
const ADDRESS_LEV2 = "村组号室队院房楼栋";
let Rules = {
	// 验证给定的值不为空
	isNonEmpty(value){
		return !!value && (value.trim() !== '');
	},
	// 姓名，只能是中文，可以包含 .·
	isName(value){
		// eslint-disable-next-line
        return !!value && (value.trim() !== '') && /^[\.·\u4e00-\u9fa5]{2,20}$/.test(value);
    },
    // 汉字，长度为2-8
    isChineseCharacters(value){
        return !!value && (value.trim() !== '') && /^[\u4e00-\u9fa5]{2,8}$/.test(value);
    },
	// 验证给定的值是否是数字
	isNumber: function(value){
		return !!value && (value.trim() !== '') && !isNaN(value);
	},
	// 非0开始的数字
	isStrictNumber(value){
		return /^[1-9]\d{1,2}$/.test(value);
	},
	// 验证给定的值是否只是字母或数字
	isAlphaNum(value){
		return !/[^a-z0-9]/i.test(value);
	},
	// 下拉框值不能为-1,'',null,undefined
	mustSelect(value){
		return !!value.trim();
	},
	// 身份证号校验
	isIdCard(value){
		return isIdCard(value);
	},
	// 邮箱校验
	isEmail(value){
		// eslint-disable-next-line
        return /^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?$/i.test(value);
	},
	// 人民币校验(可以带两位小数)
	isCurrency(value){
		return /^(?:[1-9]\d{0,16}(\.\d{1,2})?|0\.\d{1,2})$/.test(value);
	},
	// 银行卡校验
	isBankCard(value){
		return validateCardLuhn(value);
	},
	// 手机号
	isMobilePhone(value){
		return /^1[0-9]{10}$/.test(value);
	},
	// 座机
	isLandLine(value){
		return /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/.test(value);
	},

	//身份证校验男女 1：男  0：女
	isCardSex(value){
		if (value.length > 17) {
			if (value.substr(16, 1) % 2 == 1) {
				return 1;
			} else {
				return 0;
			}
		} else {
			if (value.substr(14, 1) % 2 == 1) {
				return 1;
			} else {
				return 0;
			}
		}
	},

	//社会信用代码 数字和大写字母 18位
	isCreditCode(value){
		// if(value === null || value.length !== 18){
		//     return false;
		// }else{
		//     return /^[0-9A-Z]+$/.test(value)
		// }
		let code = `${value}`;
		let reg = /^[0-9A-Z]+$/;
		if (!code || ((code.length != 18) || (reg.test(code) == false))) {
			return false;
		}

		var Ancode;//统一社会信用代码的每一个值
		var Ancodevalue;//统一社会信用代码每一个值的权重
		var total = 0;
		var weightedfactors = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28];//加权因子
		var str = '0123456789ABCDEFGHJKLMNPQRTUWXY';
		//不用I、O、S、V、Z
		for (var i = 0; i < code.length - 1; i++) {
			Ancode = code.substring(i, i + 1);
			Ancodevalue = str.indexOf(Ancode);
			total = total + Ancodevalue * weightedfactors[i];
			//权重与加权因子相乘之和
		}
		var logiccheckcode: string | number = 31 - total % 31;
		if (logiccheckcode == 31) {
			logiccheckcode = 0;
		}
		var Str = "0,1,2,3,4,5,6,7,8,9,A,B,C,D,E,F,G,H,J,K,L,M,N,P,Q,R,T,U,W,X,Y";
		var Array_Str = Str.split(',');
		logiccheckcode = Array_Str[logiccheckcode];
		var checkcode = code.substring(17, 18);
		if (logiccheckcode != checkcode) {
			return false;
		}
		return true;
	},

	// 年龄大于18岁
	isMoreThen18(age){
		return age > EIGHTEEN_YEAR_OLD;
	},


	// 18-65周岁
	isIn18and65(age){
		return age >= EIGHTEEN_YEAR_OLD && age <= 65 * ONE_YEAR_OLD;
	},

	// 28天（不含28天）-64周岁（含）
	isIn28DayAnd64Age(age){
		return age > 28  && age <= 64 * ONE_YEAR_OLD;
	},
	//28天-50周岁
	isIn28DayAnd50Age(age){
		return age >= 28  && age <= 50 * ONE_YEAR_OLD;
	},

	//30天-60周岁
	isIn30DayAnd60Age(age){
		return age >= 30  && age <= 60 * ONE_YEAR_OLD;
	},

	//60岁-80岁
	isIn60and80(age){
		return age >= 60 * ONE_YEAR_OLD && age <= 80 * ONE_YEAR_OLD;
	},

	//18到50周岁
	isIn18and50(age){
		return age >= EIGHTEEN_YEAR_OLD && age <= 50 * ONE_YEAR_OLD;
	},

	//18到60周岁
	isIn18and60(age){
		return age >= EIGHTEEN_YEAR_OLD && age <= 60 * ONE_YEAR_OLD;
	},

	//大于60岁
	isMoreThen60(age){
		return age > 60 * ONE_YEAR_OLD;
	},

	//大于50岁
	isMoreThen50(age){
		return age > 50 * ONE_YEAR_OLD;
	},

	//小于等于18岁
	isLessThenEqualTo18(age){
		return age <= EIGHTEEN_YEAR_OLD;
	},

	//大于等于18岁
	isMoreThenEqualTo18(age){
		return age >= EIGHTEEN_YEAR_OLD;
	},

	//1岁-18岁
	isIn1and18(age){
		return age >= 1 * ONE_YEAR_OLD && age <= 18 * ONE_YEAR_OLD;
	},

    isIn16and40(age){
        return age >= 16 * ONE_YEAR_OLD && age <= 40 * ONE_YEAR_OLD;
    },

	//小于51岁
	isLessThen51(age){
		return age < 51 * ONE_YEAR_OLD;
	},
	//小于50岁
	isLessThen50(age){
		return age <= 51 * ONE_YEAR_OLD;
	},
	isLessThen55(age){
		return age < 55 * ONE_YEAR_OLD;
	},
	isLessThen16(age){
		return age <= 16 * ONE_YEAR_OLD;
	},
	isLessThen18(age){
		return age <= 18 * ONE_YEAR_OLD;
	},

	/**
     * 包含以下两组，每组1个关键字

    （1） 乡|镇|街|路|道| 巷| 委| 段| 里 |社|园|苑

    （2）村|组|号|室| 队| 院| 房|楼|栋
     */
	isAddress(value){
		let flag = 0;
		for (let i = 0; i < ADDRESS_LEV1.length; i++) {
			if (value.search( ADDRESS_LEV1[i] ) >= 0) {
				flag = 1;
				break;
			}
		}
		if ( flag === 1 ) {
			for (let i = 0; i < ADDRESS_LEV2.length; i++) {
				if (value.search( ADDRESS_LEV2[i] ) >= 0) {
					flag = 2;
					break;
				}
			}
		}
		if (flag === 2) {
			return 1;
		} else {
			return 0;
		}
	},
	minLengthMatchTag(value, ...args){
		let minLength = args[0];
		if (value.length < minLength || value.search(",") >= 0 || value.search("，") >= 0) {
			return 0;
		}
		return 1;
	},
	/**
     *
     * 是否是正确的车架号：17位数字字母
     */
	isRightVin(value){
		if (value === undefined) {
			return 0;
		}

		if (value.trim().length !== 17) {
			return 0;
		}
		//纯数字
		if ( /^[0-9]*$/.test(value) ) {
			return 0;
		}
		//纯字母
		if ( /^[A-Za-z]*$/.test(value) ) {
			return 0;
		}
		return /^[A-Za-z0-9]+$/.test(value);
	},

	/**
     * 是否是正确的发动机号：数字字母
     */
	isRightengineNo(value){
		//纯数字
		if ( /^[0-9]*$/.test(value) ) {
			return 0;
		}
		//纯字母
		if ( /^[A-Za-z]*$/.test(value) ) {
			return 0;
		}
		return /^[A-Za-z0-9]+$/.test(value);
	},

	isRightPostCode(value){
		return /^[1-9][0-9]{5}$/.test(value);
	}
};
export default Rules;
