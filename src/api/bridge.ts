import { isPlainObject } from "lodash";
// import { is58App, isHaojie } from 'tools/utils';
import { is58App, isHaojie, setCookie } from "@sqb/utility";
// import {setCookie} from "tools/utils/libs/cookie";
// import pubsub from "api/pubsub";

class bridge {
    public static login() {
        return new Promise((resolve) => {
            if (is58App()) {
                window.WBAPP &&
                    window.WBAPP.invoke("login", async (resp) => {
                        const result = JSON.parse(resp);
                        if (Number(result) === 0) {
                            // 种ppu
                            window.WBAPP.invoke("get_user_info", {}, (resp) => {
                                const PPU = JSON.parse(resp).PPU;
                                if (PPU) {
                                    setCookie("PPU", PPU);
                                }
                            });

                            resolve(true);
                        } else {
                            resolve(false);
                        }

                        /*      setTimeout(() => {
                        if (Number(result) === 0) {
                            window.WBAPP.invoke('reload');
                        } else {
                            window.WBAPP.invoke('goback', { is_backtoroot: true });
                        }
                    }, 300);*/
                        /*if(pubsub.hasEvent('loginCb')) {
                        pubsub.dispatchEvent('loginCb', result);
                    } else {
                        if (Number(result) === 0) {
                            window.WBAPP.invoke('reload');
                        } else {
                            window.WBAPP.invoke('goback', { is_backtoroot: true });
                        }
                    }*/
                    });

                return;
            }

            if (isHaojie()) {
                window.__XJJSBridge__ &&
                    window.__XJJSBridge__.invoke(
                        "login_mobile_dynamic",
                        {},
                        (result) => {
                            if (Number(result) === 0) {
                                resolve(true);
                                window.location.reload();
                            } else {
                                resolve(false);
                            }
                        }
                    );
                return;
            }
        });

        // window.location.href = "";
    }

    public static setTitle(title: string = "") {
        if (is58App()) {
            document.title = title;
            window.WBAPP &&
                window.WBAPP.invoke("set_title", {
                    title,
                });
            return;
        }

        if (isHaojie()) {
            document.title = title;
            window.__XJJSBridge__ &&
                window.__XJJSBridge__.invoke("set_title", {
                    title,
                });
            return;
        }

        document.title = title;
    }

    public static setShare(cbFn?: (key: string, index: number) => void) {
        if (is58App()) {
            window.WBAPP &&
                window.WBAPP.invoke(
                    "extend_btn",
                    {
                        config: [
                            {
                                key: "share",
                                txt: "分享",
                                icon: "share",
                            },
                        ],
                        cmd: "init",
                        type: "vertical",
                    },
                    (key: string, index: number) => {
                        cbFn && cbFn(key, index);
                    }
                );
            return;
        }
    }

    public static pageTrans(url: string, options?: object) {
        try {
            if (is58App()) {
                if (!isPlainObject(options)) {
                    window.WBAPP &&
                        window.WBAPP.invoke("pagetrans", {
                            action: "pagetrans",
                            tradeline: "core",
                            content: {
                                pagetype: "common",
                                url,
                            },
                        });
                } else {
                    window.WBAPP && window.WBAPP.invoke("pagetrans", options);
                }
                return;
            }

            if (isHaojie()) {
                window.__XJJSBridge__ &&
                    window.__XJJSBridge__.invoke("pagetrans", {
                        content: {
                            url,
                            hide: false,
                        },
                    });

                return;
            }

            window.location.href = url;
        } catch (error) {
            console.log("pagetrans error", error);
        }
    }

    public static preventBack(cb: Function, times: number = 1) {
        if (isHaojie() || is58App()) {
            (function (count) {
                const listener = () => {
                    window.history.pushState(null, "", "#");
                    count++;
                    if (count === times) {
                        window.removeEventListener("popstate", listener);
                        window.history.go(-1);
                    }

                    cb();
                };

                window.history.pushState(null, "", "#");
                window.addEventListener("popstate", listener);
            })(0);

            return;
        }
    }

    /**
     * ********废弃-使用login.ts中的changeAccoutFn
     */
    // 切换账号
    // public static changeAccount() {
    //     return new Promise ( async (resolve) => {
    //         if(is58App()) {
    //             window.WBAPP && window.WBAPP.invoke('logout', async (res)=>{
    //                 const result = JSON.parse(res);
    //                 if(Number(result) === 0) {
    //                     let l = await this.login();
    //                     if (l) {
    //                         resolve(true);
    //                     } else {
    //                         resolve(false);
    //                     }
    //                 } else {
    //                     resolve(false);
    //                 }
    //             });
    //             return;
    //         }

    //         if(isHaojie()) {
    //             let l = await this.login();
    //             if (l) {
    //                 resolve(true);
    //             } else {
    //                 resolve(false);
    //             }
    //             return;
    //         }
    //     })
    // }
}

export default bridge;
