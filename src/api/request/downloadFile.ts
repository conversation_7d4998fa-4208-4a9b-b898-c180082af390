interface Params {
    reqid?: string;
    fileName?: string;
    [propName: string]: any;
}

function stringifyParams (params: Params = {}, url?: string) {
    if (!params) return null;

    // if (isNeedEncrypted(url)) {
    //     return Object.keys(params)
    //         .map((key) => key + '=' + encodeURIComponent(encrypted(params[key])))
    //         .join('&');
    // }

    return Object.keys(params)
        .map(key => key + "=" + encodeURIComponent(params[key]))
        .join("&");
}
// 过滤参数值为undefined、null、''
function _handleSpecialValue (params: Params = {}) {
    if (!params) {
        return {};
    }

    let result = {};
    if (params.__allowEmptyString === true) {   // 如果__allowEmptyString属性存在且为true，则不过滤空字符串，并将null、undefined改为空串
        for (const key in params) {
            if (key === '__allowEmptyString') { continue };
            result[key] = params[key] === undefined || params[key] === null ? '' : params[key];
        }
        return result;
    }

    Object.keys(params).forEach(item => {
        let value = params[item];
        if (
            value !== undefined &&
            value !== "undefined" &&
            value !== null &&
            value !== ""
        ) {
            result[item] = value;
        }
    });

    return result;
}

function uuidv4 () {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        var r = (Math.random() * 16) | 0;
        var v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}


/**
 * Requests a URL, returning a promise.
 */
export default {
  
    // 下载文件
    downloadFile: function (url: string, params: Params = {} , name) {
        params["reqid"] = uuidv4();
        url += "?" + stringifyParams(_handleSpecialValue(params), url);
        // window.location.href = url;
        let ele=document.createElement('a')
        ele.download=name
        ele.href=url
        document.body.appendChild(ele)
        ele.click()
        document.body.removeChild(ele)
    },
    
};
