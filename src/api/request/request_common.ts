import { Toast } from "antd-mobile";
import jsonp from "jsonp-p";
import { IResponse } from "./request_interface";
import { uuidv4, getCookie } from "@sqb/utility";
import { encrypted, decrypted, isTest } from "tools/utils/libs/encrypted";
import { loginFn } from '../login';
import { sqbSession } from "@sqb/utility";

import bindMobile from '../bindMobile';

import { API } from "../constants";
sqbSession();


/**
 * Requests a URL, returning a promise.
 *
 * @param  {string} url       The URL we want to request
 * @param  {object} [options] The options we want to pass to "fetch"
 * @return {object}           An object containing either "data" or "err"
 */

class Request {
    private constructor() { }
    private static instance: Request;

    // 需要加密的接口
    private encryptedUrls: Set<string> = new Set([
        // API.EMS, TODO: 此为举例  后续删除
        // API.register,  // 注册申请提交
        // API.insertSales, //团队管理-成员配置-新增
        // API.updateSales,   //团队管理-成员配置-编辑
        // API.insertTeam,      // 渠道管理-团队配置-新增
        // API.updateTeam      // 渠道管理-团队配置-编辑
        API.register,
        API.updateTeam,
        API.insertTeam,
        API.insertSales,
        API.updateSales
    ]);

    // 需要解密的接口
    private decryptedUrls: Set<string> = new Set([
        // API.EMS, TODO: 此为举例  后续删除
        // API.getSalesInfo,    // 注册申请回显
        // API.queryTeamMemberByPage,  //团队管理列表
        // API.getSalesTeam,   //团队管理-成员配置-回显
        // API.queryTeamByPage,   // 渠道管理列表
        API.getSalesInfo,
        API.getTeamByOne,
        API.queryTeamMemberByPage,
        API.getSalesTeam,
    ]);

    // 单例
    static getRequestInstance(): Request {
        if (!Request.instance) {
            Request.instance = new Request();
        }

        return this.instance;
    }

    /*   private parseJSON(response: any) {
        return response.json();
    }*/
    private parseJSON(response) {
        if (
            response.headers.get("Content-Type") === "text/html;charset=utf-8"
        ) {
            return { rCode: 0 };
        }
        return response.json();
    }

    private getRequestJsonBody(params = {}, url = "") {
        return this.encryptedUrls.has(url)
            ? JSON.stringify({
                data: encrypted(params).toString(),
            })
            : JSON.stringify(params);
    }

    private stringifyParams(params, url) {
        if (this.encryptedUrls.has(url)) {
            return Object.keys(params)
                .map(
                    (key) =>
                        `${key}=${encodeURIComponent(encrypted(params[key]))}`
                )
                .join("&");
        }

        return Object.keys(params)
            .map((key) => key + "=" + encodeURIComponent(params[key]))
            .join("&");
    }
    /*private stringifyParams(params: object) {
        return Object.keys(params).map((key: string): string => (key + '=' + encodeURIComponent(params[key]))).join('&');
    }*/

    private checkStatus(response: IResponse) {
        if (response.status >= 200 && response.status < 300) {
            return response;
        } else if (response.status === 404) {
            return response;
        } else {
            // message.error('出错啦,错误代码：' + response.status);
        }

        const error = new Error(response.statusText);
        // @ts-ignore
        error.response = response;
        throw error;
    }

    private decryptedData(response: IResponse, url: string): IResponse {
        const { rCode, data = "" } = response;

        if (this.decryptedUrls.has(url) && rCode === 0) {
            response.data = decrypted(data);
            return response;
        }

        return response;
    }

    /*
    private async handleData(data: any) {
        //过滤条件
        if (data === undefined || data === null || (data && data.rCode === 1)) {
            Toast.info(data.rMsg || "系统异常，请重试", 2);
        }

        //rCode === 2，登录过期，跳转登录页
        if (data && data.rCode === 2) {
            const res = await loginFn();

            return {
                ...data,
                isLogin: res,
            };
        }

        return data;
    }
    */

    private async handleData(response) {
        const { code, rCode, rMsg } = response || {};

        // 网关用code字段未登录:-1，静默登录失败:-1022 手机号未绑定:-2 由于网关有接口白名单会跳过登录校验逻辑 所以有的接口要用业务来判断是否登录
        // 业务用rCode字段：正常返回为0 未登录:-1 登录过期:2
        if (!response || rCode === undefined || rCode === null) {
            Toast.info(rMsg || "系统异常，请重试", 2);
        }

        //如果没有权限，处理登录逻辑
        if (
            code === -1 ||  // 网关未登录
            rCode === 2 ||  // 业务登录过期
            code === -1022 // 静默登录失败
        ) {
            const isLogin = await loginFn();
            return {
                ...response,
                isLogin,
            };
        }

        //没有绑定手机号 两秒内只跳转一次
        if (code === -2) {
            bindMobile();
        }

        return response;
    }

    /**
     * 将url中自带参数解析为k-v对象
     */
    private parseUrlParam() {
        let search = document.location.search;
        if (search.indexOf("?") < 0) {
            return {};
        }
        search = search.split("?")[1];
        let searchList = search.split("&");
        let urlParams = {};
        if (searchList.length > 0) {
            for (let item of searchList) {
                let keyVal = item.split("=");
                urlParams[keyVal[0]] = keyVal[1];
            }
        }
        return urlParams;
    }

    /**
     * post处理api请求中链接后的参数
     */
    private postUrlStringfy(url: string) {
        let uuid = uuidv4();
        let urlOptions = {
            reqid: uuid,
        };

        let urlParams = this.parseUrlParam();
        Object.keys(urlParams).length && Object.assign(urlOptions, urlParams);

        return this.stringifyParams(urlOptions, url);
    }

    /**
     * 合并url中的参数到body中
     * */
    private postOptsAssign(options) {
        let opts = {};
        let optsHasData: number = 0;

        options = options || { data: {} };
        if (options.data !== undefined) {
            options = options.data;
            optsHasData = 1;
        }

        let urlParams = this.parseUrlParam();
        /**
         * urlParams, options 先后顺序不可随意修改
         * @武宁 2019-12-09
         * 新增url中参数加入到body中，进行post提交
         */
        Object.assign(urlParams, options);
        if (optsHasData) {
            (opts as any).data = urlParams;
        } else {
            opts = urlParams;
        }
        return opts;
    }

    public get = (url: string, options: object = {}, headers: object = {}) => {
        // 增加nodejs识别标识
        options["reqid"] = uuidv4();
        // 获取url中携带的参数并且放入请求中
        let finalParams = this.parseUrlParam();

        Object.assign(finalParams, options);
        let params = "?";

        /*for(let key in finalParams) {
            params += key + "=" + finalParams[key] + "&";
        }*/

        params += this.stringifyParams(finalParams, url);

        return fetch(url + params, {
            method: "get",
            headers: {
                "cache-control": "no-cache",
                "referer-url": window.location.href,
                "Content-Type": "application/json; charset=utf-8",
                Accept: "application/json",
                ...headers,
            },
            credentials: "include",
        })
            .then(this.checkStatus)
            .then(this.parseJSON)
            .then((response) => this.decryptedData(response, url))
            .then(this.handleData)

    };

    public post = (url: string, options: object, headers: object = {}) => {
        //将url中参数分别放到请求的get参数和post参数中
        let opts = this.postOptsAssign(options);

        let params =
            url.indexOf("?") < 0
                ? `?${this.postUrlStringfy(url)}`
                : `&${this.postUrlStringfy(url)}`;

        return fetch(`${url}${params}`, {
            method: "post",
            headers: {
                "cache-control": "no-cache",
                "referer-url": window.location.href,
                "Content-Type": "application/json; charset=utf-8",
                // "x-csrf-token": getCookie("PPU"),
                Accept: "application/json",
                ...headers,
            },
            credentials: "include",
            body: this.getRequestJsonBody(opts, url),
        })
            .then(this.checkStatus)
            .then(this.parseJSON)
            .then((response) => this.decryptedData(response, url))
            .then(this.handleData)
            .catch((err) => ({
                err,
            }));
    };
    public postt = (url: string, options: object, headers: object = {}) => {
        // let isEncrypted = this.isNeedEncrypted(url);

        //将url中参数分别放到请求的get参数和post参数中
        // let opts = this.postOptsAssign(options);
        let params =
            url.indexOf("?") < 0
                ? `?${this.postUrlStringfy(url)}`
                : `&${this.postUrlStringfy(url)}`;

        return fetch(`${url}${params}`, {
            method: "post",
            headers: {
                "cache-control": "no-cache",
                "referer-url": window.location.href,
                "Content-Type": "application/json; charset=utf-8",
                // "x-csrf-token": getCookie("PPU"),
                Accept: "application/json",
                ...headers,
            },
            credentials: "include",
            body: this.getRequestJsonBody(options, url),
        })
            .then(this.checkStatus)
            .then(this.parseJSON)
            .then((response) => this.decryptedData(response, url))
            .then(this.handleData)
            .catch((err) => ({
                err,
            }));
    };
    public uploadFile = (url, options) => {
        return fetch(url, {
            method: 'POST',
            body: options,
            credentials: 'include',
            headers: {
                // 'x-csrf-token': getCookie('PPU'),
            }
        })
            .then(this.checkStatus)
            .then(response => response.json())
            .then((res) => {
                return this.handleData(res);
            })
            .catch(err => ({
                err
            }));
    };

    public jsonp(url: string, opts: object = {}) {
        //https://github.com/webmodules/jsonp#jsonpurl-opts-fn
        //https://www.npmjs.com/package/jsonp-p

        return jsonp(url, opts)
            .promise.then((response) => {
                return response;
            })
            .catch((error) => {
                return error;
            });
    }
}

export default Request.getRequestInstance();
