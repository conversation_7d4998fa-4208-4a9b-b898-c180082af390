import { setCookie } from '@sqb/utility';
import fetch from './request_common';

const getUserPPUAndFetch = async (url, options, headers, cb) => {
    return new Promise((resolve, reject) => {

        const timer = setTimeout(() => {
            clearTimeout(timer);
            cb(url, options, { ...headers })
                .then(res => {
                    resolve(res);
                });
        }, 500);

        window.WBAPP && window.WBAPP.invoke('get_user_info', {

        }, (resp) => {
            clearTimeout(timer);

            const { PPU } = JSON.parse(resp) || {};
            if (PPU) {
                setCookie('PPU', PPU);
            }
            cb(url, options, { ...headers })
                .then(res => {
                    resolve(res);
                });
        });
    });
};

const someHandle = (url, options, headers, cb) => {
    return new Promise((resolve, reject) => {
        resolve(getUserPPUAndFetch(url, options, headers, cb));
    });
};

export default {
    get: (url, options, headers = {}) => someHandle(url, options, headers, fetch.get),
    post: (url, options, headers = {}) => someHandle(url, options, headers, fetch.post),
    jsonp: (url, options, headers = {}) => someHandle(url, options, headers, fetch.jsonp),
};
