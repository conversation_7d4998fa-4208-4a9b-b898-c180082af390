import { isProduction, isBuildDevelopment } from "@sqb/utility";

import { BX_SALES } from "./const";
export const API = {
    //ems字段请求地址
    EMS: `//emsapi.58${isProduction && !isBuildDevelopment ? ".com" : "v5.cn"}/api/ems/get/data`,

    // 营销平台接口
    getSalesInfo: `${BX_SALES}/sales/getSalesInfo`,
    register: `${BX_SALES}/sales/register`, //注册申请提交
    queryTeamMemberByPage: `${BX_SALES}/sales/queryTeamMemberByPage`, // 团队管理-成员管理
    queryTeamMemberPerformance: `${BX_SALES}/orgInfo/queryTeamMemberPerformanceByPage`, // 团队管理-团队业绩
    queryTeamMemberManage: `${BX_SALES}/orgInfo/queryTeamMemberManage`, // 团队管理信息
    downLoadGroupPerformanceExcel: `${BX_SALES}/orgInfo/downLoadGroupPerformanceExcel`, // 团队管理-团队业绩-导出业绩
    insertSales: `${BX_SALES}/sales/insertBizStaffSales`, // 团队管理-团队成员新增
    updateSales: `${BX_SALES}/sales/updateBizStaffSales`, // 团队管理-团队成员编辑
    getSalesTeam: `${BX_SALES}/sales/getSalesTeam`, // 团队管理-团队成员回显
    getTeamProductList: `${BX_SALES}/productRecord/getTeamProductList`, // 团队管理-团队成员回显
    queryTeamByPage: `${BX_SALES}/orgInfo/queryTeamByPage`, // 渠道团队列表
    queryChannelManage: `${BX_SALES}/orgInfo/querySaleChannelInfo`, // 渠道管理信息
    queryTeamPerformance: `${BX_SALES}/orgInfo/queryTeamPerformanceByPage`, // 渠道管理团队业绩
    getTeamByOne: `${BX_SALES}/orgInfo/getTeamByOne`, // 渠道管理编辑团队回显
    insertTeam: `${BX_SALES}/orgInfo/insertTeamOrg`, // 渠道管理-新增团队
    updateTeam: `${BX_SALES}/orgInfo/updateTeamOrg`, // 渠道管理-编辑团队
    downLoadTeamPerformanceExcel: `${BX_SALES}/orgInfo/downLoadTeamPerformanceExcel`, // 渠道管理-团队业绩-导出业绩
    GET_HOME_PAGE: `${BX_SALES}/homePagesData`,

    queryHometop: `${BX_SALES}/insuranceOrder/getPerformanceCount`,//首页统计展示业绩
    // queryHometab: `${BX_SALES}/productRecord/queryProductTypeList`,//首页底部Tab
    queryHometab: `${BX_SALES}/productRecord/queryProductCategoryList`,//首页产品列表(保险, 贷款, 服务)
    queryMyCategoryType: `${BX_SALES}/productRecord/queryProductCategoryTypeList`,//获取渠道下的产品类型(保险, 贷款, 服务)
    // queryHometoggle: `${BX_SALES}/productRecord/queryByPage`,//首页底部Tab列表
    queryHometoggle: `${BX_SALES}/productRecord/queryProductList`,//首页产品列表(保险, 贷款, 服务)
    queryHomecopy: `${BX_SALES}/insuranceOrder/copyProductLink`,//首页底部Tab列表复制链接
    queryHomePlacardUrl: `${BX_SALES}/insuranceOrder/downloadPosterUrl`,//首页列表单个下载海报

    queryUserinfo: `${BX_SALES}/sales/getUserInfo`,//我的业绩个人信息
    saveOrUpdateAssistant: `${BX_SALES}/sales/saveOrUpdateAssistant`,//修改添加我的辅助调查人员
    queryUserweek: `${BX_SALES}/insuranceOrder/queryPersonalPerformanceByPage`,//个人业绩和上周业绩
    queryUserexport: `${BX_SALES}/insuranceOrder/exportPersonalPerformanceList`,//导出个人业绩
    getPosterUrlOfInviteUser: `${BX_SALES}/sales/getPosterUrlOfInviteUser`,//分享邀约海报地址
    queryPerformanceSum: `${BX_SALES}/orgInfo/queryPerformanceSum`,//团队业绩合计
    queryPersonalPremiumSum: `${BX_SALES}/insuranceOrder/queryPersonalPremiumSum`,//我的业绩合计
    queryJudgeCondition: `${BX_SALES}/sales/getJudgeCondition`,//获取邀约新成员判断条件

    // 营销平台 扫码注册前的校验
    getRegisterCheck: `${BX_SALES}/sales/registerPreCheck`,

    // 我的-贷款-业绩总览
    getPerformanceOverview: `${BX_SALES}/insuranceOrder/getPerformanceOverview`,
    // 我的-贷款-团队业绩/团队成员业绩
    getTeamPerformance: `${BX_SALES}/insuranceOrder/queryTeamPerformance`,
    // 我的-贷款-团队业绩/团队业绩详情
    getTeamMemberPerformance: `${BX_SALES}/insuranceOrder/queryTeamMemberPerformance`,
    // 贷款-本人业绩查询
    getMyselfPerformance: `${BX_SALES}/insuranceOrder/queryMyselfPerformance`,
    // 新增修改补充说明
    getAddOrUpdateExplain: `${BX_SALES}/insuranceOrder/addAndUpdateSupplyExplain`,
    // 查询补充说明
    getSupplyExplain: `${BX_SALES}/insuranceOrder/getSupplyExplain`,
    // 获取省市区信息
    getAllAdminRegions: `${BX_SALES}/insuranceOrder/getAllAdminRegions`,

    // wos token
    GET_WOS_TOKEN: `${BX_SALES}/insuranceOrder/getWosTokenByFileName`,
};