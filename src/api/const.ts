import { isBuildDevelopment } from '@sqb/utility';

console.log(process.env.REACT_APP_BUILD_TYPE, "process.env.REACT_APP_BUILD_TYPE");

const gatewayPrefix = "//benben-gateway.58.com";
// 只有 start:dev或者start:rd 会走本地接口代理
const isLocalProxy = process.env.REACT_APP_BUILD_TYPE === 'dev' || process.env.REACT_APP_BUILD_TYPE === 'rd';

// export const BX_SALES = isDevelopment || true ? '/bbmkt' : `${gatewayPrefix}/bbmkt`;
export const BX_SALES = isLocalProxy ? "/bbmkt" : `${gatewayPrefix}/bbmkt`;

//
const API = {
    //ems 配置
    EMS: `//emsapi.${isBuildDevelopment ? '58v5' : '58'}.${isBuildDevelopment ? 'cn' : 'com'}/api/ems/get/data`,

    //TODO: 微信sdk
    WECHAT_SDK: '/wechat/sdk/sign',


    // 校验贷款海报的token
    GET_CHECK_LOAN_URL: `${BX_SALES}/insuranceOrder/checkLoanUrl`,

}

export default API;

