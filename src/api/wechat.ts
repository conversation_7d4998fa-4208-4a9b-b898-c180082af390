import fetch from 'api/request';
import API from 'api/const';
const wx = window.wx;

/**
 * 接入微信SDK方式
 */
let wechatAppId = '';
if (window.location.host === 'testbaoxianm.58insure.com') {
    wechatAppId = 'wx7d537f3590005c80';
} else {
    wechatAppId = 'wx6c0d9d87733ef431';
}

class weChatFn {
    static init() {
        fetch
            .get(API.WECHAT_SDK, {
                originUrl: encodeURIComponent(window.location.href),
            })
            .then((res) => {
                res.data &&
                    wx.config({
                        appId: wechatAppId,
                        debug: window.location.search.indexOf('vconsole') >= 0,
                        timestamp: res.data.timestamp,
                        nonceStr: res.data.nonceStr,
                        signature: res.data.signature,
                        jsApiList: ['updateTimelineShareData', 'onMenuShareAppMessage'],
                    });
            });
    }

    static onMenuShare({ title, url, desc, img_url }: {title: string, url: string, desc: string, img_url?: string}) {
        wx.ready(function() {
            wx.onMenuShareAppMessage({
                title: title || '神奇保', // 分享标题
                desc: desc || '本产品由帮帮保险销售有限公司销售', // 分享描述
                link: url || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                imgUrl:
                    img_url || '//j1.58cdn.com.cn/jinrong/images/ems15674911383416ee28af1101d8.png', // 分享图标
                success: function() {
                    console.log('share success');
                },
                cancel: function () {
                    console.log('share cancel');
                }
            });

            wx.updateTimelineShareData({
                title: title || '神奇保', // 分享标题
                desc: desc || '本产品由帮帮保险销售有限公司销售', // 分享描述
                link: url || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                imgUrl:
                    img_url || '//j1.58cdn.com.cn/jinrong/images/ems15674911383416ee28af1101d8.png', // 分享图标
                success: function() {
                    console.log('share success');
                },
                cancel: function () {
                    console.log('share cancel');
                }
            });
        });
    }

    static hideOptionMenu() {
        wx.ready(function() {
            wx.hideOptionMenu();
        });
    }

    static showOptionMenu() {
        wx.ready(function() {
            wx.showOptionMenu();
        });
    }

    static getNetworkType() {
        wx.ready(function() {
            wx.getNetworkType({
                success: function(res) {
                    return res;
                },
                fail: function(res) {
                    return res;
                },
            });
        });
    }
}
// const WeChatAPI = function(){
// 	//初始化，注入信息
// 	this.init = () => {
// 		fetch.get(API.WECHAT_SDK,{
// 			originUrl: encodeURIComponent(window.location.href)
// 		}).then((res)=>{
// 			res.data && wx.config({
// 				"appId":wechatAppId,
// 				"debug": window.location.search.indexOf("vconsole") >= 0,
// 				"timestamp": res.data.timestamp,
// 				"nonceStr": res.data.nonceStr,
// 				"signature": res.data.signature,
// 				"jsApiList" : ['updateTimelineShareData', 'onMenuShareAppMessage']
// 			});
// 		});
// 	};
// 	//微信分享
// 	this.onMenuShare = function({ title,url,desc,img_url }){
// 		wx.ready(function(){
// 			wx.onMenuShareAppMessage({
// 				title: title || '神奇保', // 分享标题
// 				desc: desc || '本产品由帮帮保险销售有限公司销售', // 分享描述
// 				link: url || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
// 				imgUrl: img_url || '//j1.58cdn.com.cn/jinrong/images/ems15674911383416ee28af1101d8.png', // 分享图标
// 				success: function(){
// 					console.log("share success");
// 				}
// 			});

// 			wx.updateTimelineShareData({
// 				title: title || '神奇保', // 分享标题
// 				desc: desc || '本产品由帮帮保险销售有限公司销售', // 分享描述
// 				link: url || window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
// 				imgUrl: img_url || '//j1.58cdn.com.cn/jinrong/images/ems15674911383416ee28af1101d8.png', // 分享图标
// 				success: function(){
// 					console.log("share success");
// 				}
// 			});
// 		});
// 	};
// 	//关闭微信分享
// 	this.hideOptionMenu =  function(){
// 		wx.ready(function(){
// 			wx.hideOptionMenu();
// 		});
// 	};
// 	//打开微信分享
// 	this.showOptionMenu = function(){
// 		wx.ready(function(){
// 			wx.showOptionMenu();
// 		});
// 	};
// 	//获取微信网络状态
// 	this.getNetworkType = function(){
// 		wx.ready(function(){
// 			wx.getNetworkType({
// 		      success: function(res){
// 		        return res;
// 		      },
// 		      fail: function(res){
// 		       return res;
// 		      }
// 		    });
// 		});
// 	};
// };

export { weChatFn };
