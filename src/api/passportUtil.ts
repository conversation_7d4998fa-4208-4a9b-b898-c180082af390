
import { handleCatch, passportConfig } from '@sqb/utility';
import passportLoginDistributionplatform from 'tools/utils/libs/PassportLoginDistributionplatform';

const h5PassportLoginPromiseNew = async () => {
    try {
        const path = `${window.location.protocol}//${window.location.host}${window.location.pathname}${window.location.search}`;
        if (window.location.pathname.includes('/distributionplatform')) {
            //营销平台自定义登录页
            window.location.href = `${window.location.protocol}//${window.location.host}/agentapp/distributionplatform/Login?path=${encodeURIComponent(
                path
            )}`;
        } else {
            //其他平台 跳 passport默认登录页
            const loginUrl = `${window.location.protocol}//passport.58.com/m/login`;
            const href = `${loginUrl}?path=${encodeURIComponent(
                path
            )}&source=58-insurem-m&isredirect=true`;
            window.location.href = href;
        }
        //未登录
        // window.location.href = href;

        return {};
    } catch (error) {
        handleCatch(error);
        return error;
    }
};

const passportInit = () => {
    window.sdk_m_init &&
        window.sdk_m_init(passportConfig, (result) => {
            if (result.code === 0) {
            }
        });
};
const passportDistributionplatformInit = (path) => {
    if (path) {
        passportLoginDistributionplatform.path = path;
    }
    console.log('passportLoginDistributionplatform', passportLoginDistributionplatform);
    window.sdk_m_init &&
        window.sdk_m_init(passportLoginDistributionplatform, (result) => {
            if (result.code === 0) {
            }
        });
};
export {
    // h5PassportLoginPromise,
    passportInit,
    h5PassportLoginPromiseNew,
    passportDistributionplatformInit,
};
