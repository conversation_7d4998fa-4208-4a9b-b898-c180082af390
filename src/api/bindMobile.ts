import { debounce } from "lodash";
import <PERSON><PERSON><PERSON> from "hybrid/core";
import { is58App } from "@sqb/utility";

const bindMobile = debounce(
    () => {
        if (is58App()) {
            WBAPP.invoke("bind_account", {
                type: "PHONE",
            }, function (result) {
                if (`${result}` === `0`) {
                    window.location.reload();
                } else {
                    setTimeout(() => {
                        window.history.back();
                    }, 1);
                }
            }
            );
        } else {
            const { protocol, href } = window.location;
            const bind_account = `${protocol}//m.m.58.com/open/mobilebind`;
            const link = `${bind_account}?path=${encodeURIComponent(href)}`;
            window.location.href = link;
        }
    }, 1500, { 
        leading: true,
        trailing: false,
    }
);

export default bindMobile;