/**
 * 分享组件， 以后有分享的功能可以直接调用该组件
 *
 * 支持
 * 微信点击分享
 * 58同城
 *
 * */

// import { is58App, isWechat } from 'tools/utils';
import { renderDom } from "commons/ShareModal";
import { Toast } from 'antd-mobile';
import { weChatFn, is58App, isWechat } from '@sqb/utility';

//58分享 http://iwiki.58corp.com/wiki/wxfe1/view/%E5%AF%B9%E5%A4%96%E6%96%87%E6%A1%A3/Hybrid/1.%E9%80%9A%E7%94%A8%E6%80%A7%E7%BB%84%E4%BB%B6%E6%96%87%E6%A1%A3/share%EF%BC%9A%E8%B0%83%E7%94%A8%E5%88%86%E4%BA%AB%E7%BB%84%E4%BB%B6/

// 参数按照58同城的来
interface IConfig {
    img_url?: string,
    url?: string,
    title?: string,
    content?: string,
    ["propsName"]?: any
}

// 58同城分享的渠道 默认 所有的
type typeShareTo = ("WEIXIN"|"FRIENDS"|"SINA"|"QQ")[] & any;

let shareToDefault = is58App() ? ["WEIXIN", "FRIENDS", "SINA", "QQ"] : []


// const dom = `<div><img src="" alt=""/></div>`;



const share = (config: IConfig, shareTo: typeShareTo = shareToDefault) => {
    shareTo = shareTo ? shareTo : shareToDefault;
    if(isWechat()) {
        // todo 这个地方是否需要写
        weChatFn.onMenuShare({
            title: config.title || '',
            url: config.url || window.location.href,
            desc: config.content || '',
            img_url: config.img_url || '',
        });

        renderDom();

        return;
    }

    if(is58App()) {
        let share58Config = Array.isArray(shareTo)
            ? shareTo.map(item => {
                return {
                    shareto: item,
                    ...config
                }
            }) : []

        window.WBAPP && window.WBAPP.invoke('share',{
            config: share58Config
        },function(state,source){
            // 分享成功
            if(state ==='0'){
            }
            // 取消分享
            if(state ==='2'){
            }
            // 分享失败
            if(state ==='1'){
            }
            // 应用未安转
            if(state ==='4'){
            }

        });

        return;
    }

    Toast.info('不支持分享', 2);
}

export default share;
