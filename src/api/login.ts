// import { is58App, is<PERSON><PERSON>ji<PERSON> } from 'tools/utils';
// import { setCookie } from 'tools/utils/libs/cookie';
import { getPassportUrl, is58App, isHaojie, setCookie} from '@sqb/utility';
import { h5PassportLoginPromiseNew } from './passportUtil';
/**
 * 通用登录
 * @returns Promise 登录态
 */
const loginFn = (config?: { h5Config: any }) => {
    try {
        return new Promise(async (resolve)=>{
            if (is58App()) {
                window.WBAPP &&
                    window.WBAPP.invoke('login', {}, (resp) => {
                        const result = JSON.parse(resp);
                        if (Number(result) === 0) {
                            // 种ppu
                            window.WBAPP && window.WBAPP.invoke('get_user_info', {}, (resp) => {
                                const PPU = JSON.parse(resp).PPU;
                                if (PPU) {
                                    setCookie('PPU', PPU);
                                }
                            });
                            resolve(true);
                            window.location.reload();
                        } else {
                            resolve(false);
                        }
                    });

                return;
            }

            if (isHaojie()) {
                window.__XJJSBridge__ &&
                    window.__XJJSBridge__.invoke('login_mobile_dynamic', {}, (result) => {
                        if (Number(result) === 0) {
                            resolve(true);
                            window.location.reload();
                        } else {
                            resolve(false);
                        }
                    });
                return;
            }
            // 非58体系调用，则使用h5 passport登录
            let { isLogin } = await h5PassportLoginPromiseNew();
            resolve(isLogin);
        })
    } catch (error) {
        console.log(error);
        return Promise.resolve(false);
    }
};

/**
 * 切换登录账号
 */
const changeAccountFn = () => {
        if(is58App()) {
            window.WBAPP && window.WBAPP.invoke('logout', async (res)=>{
                const result = JSON.parse(res);
                if(Number(result) === 0) {
                    loginFn();
                }
            });
            return;
        }

        if(isHaojie()) {
            loginFn();
            return;
        }
};

export {
     loginFn,
     changeAccountFn,
}
