@charset "UTF-8";

/// anim 属性map
/// @group animation
/// @access private
@mixin m-anim-attr ($configMap) {
    @each $key, $value in $configMap {
        -webkit-animation-#{$key}: $value;
        animation-#{$key}: $value;
    };
}

/// 闪烁动画
/// @param {Map} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
/// @example flash - 直接使用:
///     scss file:
///     .example {
///         @include m-anim-flash();
///     }
///     css file:
///     .example {
///         -webkit-animation-iteration-count: 1;
///         animation-iteration-count: 1;
///         -webkit-animation-fill-mode: both;
///         animation-fill-mode: both;
///         -webkit-animation-name: m-flash;
///         animation-name: m-flash;
///         -webkit-animation-duration: 1s;
///         animation-duration: 1s; }
///     @-webkit-keyframes {
///         0%,100%,50% {
///             opacity: 1; }
///         25%,75% {
///             opacity: 0; } }
///    @keyframes {
///         0%,100%,50% {
///             opacity: 1; }
///         25%,75% {
///             opacity: 0; } }
///
/// @example flash - 含参数:
///     scss file:
///     .example {
///         @include m-anim-flash((
///             duration: 2s,
///             fill-mode: both,
///             name: m-flash-cus
///         ), true) {
///             0%,100%,50% {
///                 opacity: .6
///             }
///             25%,75% {
///                 opacity: 0
///             }
///         };
///     }
///
///     css file:
///     .example {
///         -webkit-animation-iteration-count: 1;
///         animation-iteration-count: 1;
///         -webkit-animation-fill-mode: both;
///         animation-fill-mode: both;
///         -webkit-animation-name: m-flash-cus;
///         animation-name: m-flash-cus;
///         -webkit-animation-duration: 2s;
///         animation-duration: 2s; }
///     @-webkit-keyframes m-flash-cus {
///         0%,100%,50% {
///             opacity: 0.6; }
///         25%,75% {
///             opacity: 0; } }
///     @keyframes m-flash-cus {
///         0%,100%,50% {
///             opacity: 0.6; }
///         25%,75% {
///             opacity: 0; } }
/// @access public
@mixin m-anim-flash($configMap:(), $custom:false) {
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-flash,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0%,100%,50% {
                    opacity: 1
                }
                25%,75% {
                    opacity: 0
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0%,100%,50% {
                    opacity: 1
                }
                25%,75% {
                    opacity: 0
                }
            }
        }
    };
}

/// 渐现动画
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-fade-in($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-fade-in,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {opacity: 0;}
                100% {opacity: 1;}
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {opacity: 0;}
                100% {opacity: 1;}
            }
        }
    };
}

/// 弹性渐现动画
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-bounce-in($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-bounce-in,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    opacity: 0;
                    -webkit-transform: scale3d(.3, .3, .3);
                }
                20% {
                    -webkit-transform: scale3d(1.1, 1.1, 1.1);
                }
                40% {
                    -webkit-transform: scale3d(.9, .9, .9);
                }
                60% {
                    opacity: 1;
                    -webkit-transform: scale3d(1.03, 1.03, 1.03);
                }
                80% {
                    -webkit-transform: scale3d(.97, .97, .97);
                }
                100% {
                    opacity: 1;
                    -webkit-transform: scale3d(1, 1, 1);
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    opacity: 0;
                    transform: scale3d(.3, .3, .3);
                }
                20% {
                    transform: scale3d(1.1, 1.1, 1.1);
                }
                40% {
                    transform: scale3d(.9, .9, .9);
                }
                60% {
                    opacity: 1;
                    transform: scale3d(1.03, 1.03, 1.03);
                }
                80% {
                    transform: scale3d(.97, .97, .97);
                }
                100% {
                    opacity: 1;
                    transform: scale3d(1, 1, 1);
                }
            }
        }
    };
}

/// 模糊渐现动画
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-blur-in($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-blur-in,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    opacity: 0;
                    -webkit-transform-origin: 50% 50%;
                    -webkit-transform: scale(2, 2);
                    filter: blur(100px);
                }
                to {
                    opacity: 1;
                    -webkit-transform-origin: 50% 50%;
                    -webkit-transform: scale(1, 1);
                    filter: blur(0px);
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    opacity: 0;
                    transform-origin: 50% 50%;
                    transform: scale(2, 2);
                    filter: blur(100px);
                }
                to {
                    opacity: 1;
                    transform-origin: 50% 50%;
                    transform: scale(1, 1);
                    filter: blur(0px);
                }
            }
        }
    };
}

/// 水平翻转渐现动画
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-flipx-in($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-flipx-in,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    -webkit-transform: perspective(400px) translate3d(0, 0, 20px) rotateX(60deg) ;
                    opacity: 0
                }
                30% {
                    -webkit-transform: perspective(400px) translate3d(0, 0, 20px) rotateX(-6deg)
                }
                75% {
                    -webkit-transform: perspective(400px) translate3d(0, 0, 10px) rotateX(3deg)
                }
                100% {
                    -webkit-transform: perspective(400px) translate3d(0, 0, 2px) rotateX(0deg);
                    opacity: 1
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    transform: perspective(400px) translate3d(0, 0, 20px) rotateX(60deg);
                    opacity: 0
                }
                30% {
                    transform: perspective(400px) translate3d(0, 0, 20px) rotateX(-6deg)
                }
                75% {
                    transform: perspective(400px) translate3d(0, 0, 10px) rotateX(3deg)
                }
                100% {
                    transform: perspective(400px) translate3d(0, 0, 2px) rotateX(0deg);
                    opacity: 1
                }
            }
        }
    };
}

/// 垂直翻转渐现动画
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-flipy-in($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-flipy-in,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    -webkit-transform: perspective(400px) translate3d(0, 0, 20px) rotateY(60deg);
                    opacity: 0
                }
                30% {
                    -webkit-transform: perspective(400px) translate3d(0, 0, 20px) rotateY(-6deg)
                }
                75% {
                    -webkit-transform: perspective(400px) translate3d(0, 0, 10px) rotateY(3deg)
                }
                100% {
                    -webkit-transform: perspective(400px) translate3d(0, 0, 2px) rotateY(0deg);
                    opacity: 1
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    transform: perspective(400px) translate3d(0, 0, 20px) rotateY(60deg);
                    opacity: 0
                }
                30% {
                    transform: perspective(400px) translate3d(0, 0, 20px) rotateY(-6deg)
                }
                75% {
                    transform: perspective(400px) translate3d(0, 0, 10px) rotateY(3deg)
                }
                100% {
                    transform: perspective(400px) translate3d(0, 0, 2px) rotateY(0deg);
                    opacity: 1
                }
            }
        }
    };
}

/// 报纸飞出动效
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-newspaper($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-newspaper,
        duration: 0.5s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    -webkit-transform: scale(0) rotate(720deg);
                    opacity: 0;
                }
                to {
                    -webkit-transform: scale(1) rotate(0deg);
                    opacity: 1;
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    transform: scale(0) rotate(720deg);
                    opacity: 0;
                }
                to {
                    transform: scale(1) rotate(0deg);
                    opacity: 1;
                }
            }
        }
    };
}

/// 弹性落下效果
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-bounce-down($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-bounce-down,
        duration: 0.5s,
        timing-function: cubic-bezier(0.2, 0.6, 0.35, 1.000)
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    opacity: 0;
                    -webkit-transform: translate3d(0, -3000px, 0);
                }
                60% {
                    opacity: 1;
                    -webkit-transform: translate3d(0, 25px, 0);
                }
                75% {
                    -webkit-transform: translate3d(0, -10px, 0);
                }
                90% {
                    -webkit-transform: translate3d(0, 5px, 0);
                }
                to {
                    -webkit-transform: none;
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    opacity: 0;
                    transform: translate3d(0, -3000px, 0);
                }
                60% {
                    opacity: 1;
                    transform: translate3d(0, 25px, 0);
                }
                75% {
                    transform: translate3d(0, -10px, 0);
                }
                90% {
                    transform: translate3d(0, 5px, 0);
                }
                to {
                    transform: none;
                }
            }
        }
    };
}

/// 3D撕裂飞出(要求parent已经设置perspective,默认请设置为3000px)
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-slit($configMap:(), $custom: false){
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-transform: translateZ(-3000px) rotateY(90deg);
    transform: translateZ(-3000px) rotateY(90deg);
    $defaultConf: (
        iteration-count: 1,
        name: m-slit,
        duration: 0.7s,
        timing-function: ease-out,
        fill-mode: forwards
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                50% {
                    -webkit-transform: translateZ(-250px) rotateY(89deg);
                    opacity: .5;
                }
                100% {
                    -webkit-transform: translateZ(0) rotateY(0deg);
                    opacity: 1;
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                50% {
                    transform: translateZ(-250px) rotateY(89deg);
                    opacity: .5;
                }
                100% {
                    transform: translateZ(0) rotateY(0deg);
                    opacity: 1;
                }
            }
        }
    }
}

/// 3D掉落飞出(要求parent已经设置perspective,默认请设置为1300px)
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-fall($configMap:(), $custom: false){
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    $defaultConf: (
        iteration-count: 1,
        name: m-fall,
        duration: 0.7s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    -webkit-transform: translateZ(600px) rotateX(40deg);
                    opacity: 0;
                }
                to {
                    -webkit-transform: translateZ(0px) rotateX(0deg);
                    opacity: 1;
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    transform: translateZ(600px) rotateX(40deg);
                    opacity: 0;
                }
                to {
                    transform: translateZ(0px) rotateX(0deg);
                    opacity: 1;
                }
            }
        }
    }
}

/// 虚化渐现
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-vanish-in($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        name: m-vanishIn,
        duration: 0.7s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    opacity: 0;
                    -webkit-transform-origin: 50% 50%;
                    -webkit-transform: scale(2, 2);
                    -webkit-filter: blur(90px);
                }

                100% {
                    opacity: 1;
                    -webkit-transform-origin: 50% 50%;
                    -webkit-transform: scale(1, 1);
                    -webkit-filter: blur(0px);
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    opacity: 0;
                    transform-origin: 50% 50%;
                    transform: scale(2, 2);
                    -webkit-filter: blur(90px); // 目前滤镜一定要加webkit前缀
                }
                100% {
                    opacity: 1;
                    transform-origin: 50% 50%;
                    transform: scale(1, 1);
                    -webkit-filter: blur(0px); // 目前滤镜一定要加webkit前缀
                }
            }
        }
    }
}

/// 位移+缩放消失
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-foolish-out($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: 1,
        name: m-foolishOut,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    opacity: 1;
                    -webkit-transform-origin: 50% 50%;
                    -webkit-transform: scale(1, 1) rotate(360deg);
                }
                20% {
                    opacity: 1;
                    -webkit-transform-origin: 0% 0%;
                    -webkit-transform: scale(0.5, 0.5) rotate(0deg);
                }
                40% {
                    opacity: 1;
                    -webkit-transform-origin: 100% 0%;
                    -webkit-transform: scale(0.5, 0.5) rotate(0deg);
                }
                60% {
                    opacity: 1;
                    -webkit-transform-origin: 0%;
                    -webkit-transform: scale(0.5, 0.5) rotate(0deg);
                }
                80% {
                    opacity: 1;
                    -webkit-transform-origin: 0% 100%;
                    -webkit-transform: scale(0.5, 0.5) rotate(0deg);
                }
                100% {
                    opacity: 0;
                    -webkit-transform-origin: 50% 50%;
                    -webkit-transform: scale(0, 0) rotate(0deg);
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    opacity: 1;
                    transform-origin: 50% 50%;
                    transform: scale(1, 1) rotate(360deg);
                }
                20% {
                    opacity: 1;
                    transform-origin: 0% 0%;
                    transform: scale(0.5, 0.5) rotate(0deg);
                }
                40% {
                    opacity: 1;
                    transform-origin: 100% 0%;
                    transform: scale(0.5, 0.5) rotate(0deg);
                }
                60% {
                    opacity: 1;
                    transform-origin: 0%;
                    transform: scale(0.5, 0.5) rotate(0deg);
                }
                80% {
                    opacity: 1;
                    transform-origin: 0% 100%;
                    transform: scale(0.5, 0.5) rotate(0deg);
                }
                100% {
                    opacity: 0;
                    transform-origin: 50% 50%;
                    transform: scale(0, 0) rotate(0deg);
                }
            }
        }
    }
}


/// tada
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-tada($configMap:(), $custom: false) {
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-tada,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    -webkit-transform: scale3d(1, 1, 1);
                }

                10%, 20% {
                    -webkit-transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
                }

                30%, 50%, 70%, 90% {
                    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
                }

                40%, 60%, 80% {
                    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
                }

                100% {
                    -webkit-transform: scale3d(1, 1, 1);
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                0% {
                    transform: scale3d(1, 1, 1);
                }

                10%, 20% {
                    transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg);
                }

                30%, 50%, 70%, 90% {
                    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
                }

                40%, 60%, 80% {
                    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
                }

                100% {
                    transform: scale3d(1, 1, 1);
                }
            }
        }
    };
}

/// 橡皮效果
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-rubber($configMap:(), $custom: false) {
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-rubber,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                }
                30% {
                    -webkit-transform: scale3d(1.25, 0.75, 1);
                    transform: scale3d(1.25, 0.75, 1);
                }
                40% {
                    -webkit-transform: scale3d(0.75, 1.25, 1);
                    transform: scale3d(0.75, 1.25, 1);
                }
                50% {
                    -webkit-transform: scale3d(1.15, 0.85, 1);
                    transform: scale3d(1.15, 0.85, 1);
                }
                65% {
                    -webkit-transform: scale3d(.95, 1.05, 1);
                    transform: scale3d(.95, 1.05, 1);
                }
                75% {
                    -webkit-transform: scale3d(1.05, .95, 1);
                    transform: scale3d(1.05, .95, 1);
                }
                to {
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                }
                30% {
                    -webkit-transform: scale3d(1.25, 0.75, 1);
                    transform: scale3d(1.25, 0.75, 1);
                }
                40% {
                    -webkit-transform: scale3d(0.75, 1.25, 1);
                    transform: scale3d(0.75, 1.25, 1);
                }
                50% {
                    -webkit-transform: scale3d(1.15, 0.85, 1);
                    transform: scale3d(1.15, 0.85, 1);
                }
                65% {
                    -webkit-transform: scale3d(.95, 1.05, 1);
                    transform: scale3d(.95, 1.05, 1);
                }
                75% {
                    -webkit-transform: scale3d(1.05, .95, 1);
                    transform: scale3d(1.05, .95, 1);
                }
                to {
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                }
            }
        }
    };
}


/// 果冻效果
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-jello($configMap:(), $custom: false) {
    -webkit-transform-origin: center;
    transform-origin: center;
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-jello,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from, 11.1%, to {
                    -webkit-transform: none;
                    transform: none;
                }
                22.2% {
                    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
                    transform: skewX(-12.5deg) skewY(-12.5deg);
                }
                33.3% {
                    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
                    transform: skewX(6.25deg) skewY(6.25deg);
                }
                44.4% {
                    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
                    transform: skewX(-3.125deg) skewY(-3.125deg);
                }
                55.5% {
                    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
                    transform: skewX(1.5625deg) skewY(1.5625deg);
                }
                66.6% {
                    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
                    transform: skewX(-0.78125deg) skewY(-0.78125deg);
                }
                77.7% {
                    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
                    transform: skewX(0.390625deg) skewY(0.390625deg);
                }
                88.8% {
                    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
                    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from, 11.1%, to {
                    -webkit-transform: none;
                    transform: none;
                }
                22.2% {
                    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
                    transform: skewX(-12.5deg) skewY(-12.5deg);
                }
                33.3% {
                    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
                    transform: skewX(6.25deg) skewY(6.25deg);
                }
                44.4% {
                    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
                    transform: skewX(-3.125deg) skewY(-3.125deg);
                }
                55.5% {
                    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
                    transform: skewX(1.5625deg) skewY(1.5625deg);
                }
                66.6% {
                    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
                    transform: skewX(-0.78125deg) skewY(-0.78125deg);
                }
                77.7% {
                    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
                    transform: skewX(0.390625deg) skewY(0.390625deg);
                }
                88.8% {
                    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
                    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
                }
            }
        }
    };
}


/// 脉冲效果
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-pulse($configMap:(), $custom: false) {
    -webkit-transform-origin: center;
    transform-origin: center;
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-pulse,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                }
                70% {
                    -webkit-transform: scale3d(1.15, 1.15, 1.15);
                    transform: scale3d(1.15, 1.15, 1.15);
                }
                to {
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                }
                70% {
                    -webkit-transform: scale3d(1.15, 1.15, 1.15);
                    transform: scale3d(1.15, 1.15, 1.15);
                }
                to {
                    -webkit-transform: scale3d(1, 1, 1);
                    transform: scale3d(1, 1, 1);
                }
            }
        }
    };
}



/// 位移动画
/// @param {Object} $configMap 配置map
/// @param {String} $direction [down] 运动方向
/// @param {Number} $distance [3000px] 运动距离
/// @group animation
@mixin m-anim-move($configMap:(), $direction: down, $distance: 3000px){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-move,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($direction == up) {
                from {
                    -webkit-transform: translate3d(0, $distance, 0);
                    transform: translate3d(0, $distance, 0)
                }
                to {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
            } @else if ($direction == down){
                from {
                    -webkit-transform: translate3d(0, $distance, 0);
                    transform: translate3d(0, $distance, 0)
                }
                to {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
            } @else if ($direction == left){
                from {
                    -webkit-transform: translate3d($distance, 0, 0);
                    transform: translate3d($distance, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
            } @else if ($direction == right){
                from {
                    -webkit-transform: translate3d(-$distance, 0, 0);
                    transform: translate3d(-$distance, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($direction == up) {
                from {
                    -webkit-transform: translate3d(0, $distance, 0);
                    transform: translate3d(0, $distance, 0)
                }
                to {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
            } @else if ($direction == down){
                from {
                    -webkit-transform: translate3d(0, $distance, 0);
                    transform: translate3d(0, $distance, 0)
                }
                to {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
            } @else if ($direction == left){
                from {
                    -webkit-transform: translate3d($distance, 0, 0);
                    transform: translate3d($distance, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
            } @else if ($direction == right){
                from {
                    -webkit-transform: translate3d(-$distance, 0, 0);
                    transform: translate3d(-$distance, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
            }
        }
    };
}
@mixin m-anim-move-out($configMap:(), $direction: down, $distance: 3000px){
    $defaultConf: (
        iteration-count: 1,
        fill-mode: both,
        name: m-move-out,
        duration: 1s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($direction == up) {
                from {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(0, -$distance, 0);
                    transform: translate3d(0, -$distance, 0)
                }
            } @else if ($direction == down){
                from {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(0, -$distance, 0);
                    transform: translate3d(0, -$distance, 0)
                }
            } @else if ($direction == left){
                from {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(-$distance, 0, 0);
                    transform: translate3d(-$distance, 0, 0)
                }
            } @else if ($direction == right){
                from {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(-$distance, 0, 0);
                    transform: translate3d(-$distance, 0, 0)

                }
            }
        }
        @keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($direction == up) {
                from {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(0, -$distance, 0);
                    transform: translate3d(0, -$distance, 0)
                }
            } @else if ($direction == down){
                from {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(0, -$distance, 0);
                    transform: translate3d(0, -$distance, 0)
                }
            } @else if ($direction == left){
                from {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(-$distance, 0, 0);
                    transform: translate3d(-$distance, 0, 0)
                }
            } @else if ($direction == right){
                from {
                    -webkit-transform: translate3d(0, 0, 0);
                    transform: translate3d(0, 0, 0)
                }
                to {
                    -webkit-transform: translate3d(-$distance, 0, 0);
                    transform: translate3d(-$distance, 0, 0)

                }
            }
        }
    };
}


/// 彩虹滤镜动画(目前滤镜一定要加webkit前缀)
/// @param {Object} $configMap 配置map
/// @param {Boolean} $custom [false] 是否自定义动画帧
/// @group animation
@mixin m-anim-rainbow($configMap:(), $custom: false){
    $defaultConf: (
        iteration-count: infinite,
        fill-mode: both,
        name: m-rainbow,
        duration: 4s
    );
    @include m-anim-attr(map-merge($defaultConf, $configMap));
    @at-root(without: all) {
        @-webkit-keyframes #{map-get(map-merge($defaultConf, $configMap), name)} {
            @if ($custom) {
                @content;
            } @else {
                from {
                    -webkit-filter: hue-rotate(0deg);
                }
                to {
                    -webkit-filter: hue-rotate(-360deg);
                }
            }
        }
    };
}

/// 菊花loading动效
/// @group animation
@mixin m-chrysanthemum-spinner(){
    @at-root(without: all) {
        @-webkit-keyframes m-chrysanthemum-spinner {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg)
            }

            50% {
                -webkit-transform: rotate(180deg);
                transform: rotate(180deg)
            }

            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg)
            }
        }

        @keyframes m-chrysanthemum-spinner {
            0% {
                -webkit-transform: rotate(0deg);
                transform: rotate(0deg)
            }

            50% {
                -webkit-transform: rotate(180deg);
                transform: rotate(180deg)
            }

            100% {
                -webkit-transform: rotate(360deg);
                transform: rotate(360deg)
            }
        }
    };
}
