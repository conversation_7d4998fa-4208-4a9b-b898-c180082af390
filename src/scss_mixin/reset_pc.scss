@charset "UTF-8";

body, p, ul, ol, li, dl, dt, dd, h1, h2, h3, h4, h5, h6, form, div, span, i, em, strong, b, fieldset, legend, input, select, textarea, button, th, td, menu{
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    -webkit-tap-highlight-color: transparent;
    outline: none;
    margin: 0;
    padding: 0;
    -webkit-text-size-adjust: none; // 横屏时候的字体变大问题
    text-size-adjust: none;
}
body {
    font-family: "Helvetica Neue",Helvetica,STHeiTi,sans-serif;
    font-size: 16px;
    color: #333;
    //background-color: #efeff4;
    -webkit-overflow-scrolling: touch;
    //max-width: 720px;
    margin: 0 auto;
    min-height: 100%;
    //-webkit-user-select: none;//禁止选择文字
    //@include box-shadow(0 0 10px rgba(0,0,0,0.2));
    margin: 0 auto;
}
// Universal link styling
a {
    //color: #002be5;
    text-decoration: none;
    outline: none;
    cursor: pointer;
    -webkit-tap-highlight-color: none;
    &:active {
        //background: #ededed;
        outline: 0;
    }
}

//input,
//select,
//button,
//textarea {
//    background-color: transparent;
//    outline: none;
//    border: 0;
//    //-webkit-appearance: none;
//    font-family: inherit;
//}
//input::-webkit-input-placeholder {
//    color: #bdbdbd;
//}
button[disabled], html input[disabled] {
    cursor: default;
}
button, input {
    line-height: normal;
}
button, select {
    text-transform: none;
}

ul, ol, li {
    list-style: none;
}
i,
strong,
em {
    font-weight: normal;
}
img,
fieldset {
    border: 0;
}

.fr {
    float: right !important;
}

.fl {
    float: left !important;
}

.visible {
    visibility: visible;
}
.hide,.hidden {
    display: none !important;
}
.clearfix:before, .clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both;
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
