import React, { RefObject, useRef, forwardRef, useImperativeHandle } from 'react'
import { Form, Input, DatePicker, Picker, Toast, Cascader } from 'antd-mobile-v5';
import "./index.scss";
import dayjs from 'dayjs'
import type { DatePickerRef } from 'antd-mobile-v5/es/components/date-picker';
import { PickerRef } from 'antd-mobile-v5/es/components/picker';
import _default, { PickerColumn } from 'antd-mobile-v5/es/components/picker-view';
import { PickerValueExtend, PickerValue } from 'antd-mobile-v5/es/components/picker-view';
import { Rule } from 'antd-mobile-v5/es/components/form';
// CascaderOption
import { CascaderOption } from 'antd-mobile-v5/es/components/cascader-view';
import TextArea from 'commons/TextArea/TextArea';
// FormInstance<any>
type IBaseForm = { form: any, formColumn: IForm, disabled?: boolean }

// 目前已经封装的组建类型 后续有需要可自行扩展
type IFormType = "input" | "picker" | "datePicker" | "cascader" | "customPicker" | "textarea";
export type IFormItem = {
    type: IFormType,
    // 传参的键名
    name?: string,
    // 只有input需要
    fieldType?: "number",
    label: React.ReactNode,
    placeholder?: string,
    rules?: Rule[],
    // 输入或者选中值的文本位置
    align?: "left" | "right",
    // picker cascader 才有
    column?: PickerColumn,
    options?: CascaderOption[],
    // 主要针对pickervalue cascader的改变
    onConfirm?: (value: PickerValue, source?: PickerValueExtend) => void,
    // input
    onChange?: (value: string) => void,
    // 是否需要获取 picker value对应的label
    labelValue?: boolean,
    [props: string]: any

}
export type IForm = {
    title?: React.ReactNode,
    fields: IFormItem[]
}[];

type AllType = {
    [propsname: string]: any
}
type RefSet = RefObject<Set<string | undefined>>
// 格式化时间 DatePicker接受的数据类型为Date类型 对于不是Date类型的 将它转化为Date格式
function formatTime(value: Date | string) {
    if (!value) return;
    if (value instanceof Date) {
        return value
    }
    return new Date(dayjs(value).valueOf())

}
// column picker的colunms只接受二维数组  这个方法用来支持一维数组
function formatPickerColumns(column?: PickerColumn) {
    if (!column || column.length === 0) {
        return []
    }
    if (column[0] instanceof Array) {
        return column
    } else {
        return [column]
    }

}
const MyPicker = forwardRef<PickerRef, IFormItem>(({ value, onClick, ...restProps }, ref) => {
    const { format = "YYYY-MM-DD" } = restProps
    return (
        <>
            <DatePicker
                {...restProps as any}
                value={formatTime(value)}
                ref={ref as any}
            >
                {(value, actions) => {
                    return (
                        <>
                            {value ? (
                                <span
                                    onClick={() => {
                                        onClick && onClick();
                                        actions.open();
                                    }}
                                >
                                    {dayjs(value, format).format(format)}{" "}
                                </span>
                            ) : (
                                <span
                                    onClick={() => {
                                        onClick && onClick();
                                        actions.open();
                                    }}
                                    className="place_holder"
                                >
                                    请选择
                                </span>
                            )}
                        </>
                    );
                }}
            </DatePicker>
        </>)
})
function formatSelect(value: Array<any>, key: string = "", pickerValueRef: RefObject<AllType>, field: IFormItem,) {
    // console.log(value, "===value");

    let valueArray = [];
    let labelArray: any = []


    if (!value || !Array.isArray(value) || value.length === 0 || !value[0]?.label) {
        return {
            valueArray,
            labelArray,
            valueStr: labelArray.join("-")
        }
    }
    value.forEach((item) => {
        return labelArray.push(item.label);
    })
    if (field.labelValue) {
        // @ts-ignore
        pickerValueRef?.current[key + 'LabelArr'] = labelArray;

    }

    // current[key + 'NameArr'] = labelArray;
    return {
        valueArray,
        labelArray,
        valueStr: labelArray.join("-")
    }
}
function isEmpty(value: any) {
    return value === "" || value === null || value === undefined
}
function pickerValueToArray(value: any) {
    if (Array.isArray(value)) {
        return value
    }
    return [value]
}
// 对于只有一列数据的picker 将其返回值处理为原始类型
function pickerArrayToValue(value: any) {
    if (Array.isArray(value) && value.length === 1) {
        return value[0]
    }
    return value
}
// column picker的回显只接受数组格式   这个方法用来支持原始类型的value
function handlePickerReshowValues(reshowValue: AllType, pickerKeys: RefSet) {
    let newObj = { ...reshowValue };
    for (let key in reshowValue) {
        if (pickerKeys.current?.has(key)) {
            const value = newObj[key];
            isEmpty(value)
                ? (delete newObj[key])
                : (newObj[key] = pickerValueToArray(value))
        }
    }
    return newObj;
}
function handlePickerValues(values: AllType, pickerKeys: RefSet) {
    let newObj = { ...values };
    for (let key in values) {
        if (pickerKeys.current?.has(key)) {
            const value = newObj[key];
            if (!isEmpty(value)) {
                (newObj[key] = pickerArrayToValue(value))

            }

        }
    }
    return newObj;

}
function getPickerKeys(formColumn: IForm, pickerKeys: RefSet) {

    formColumn.forEach(item => {
        item.fields.forEach(field => {
            if (field.type === "picker") {
                pickerKeys.current?.add(field?.name)
            }
        })
    })

}


const formTypeMap = {
    input: (item: IFormItem) => {
        const { label, name, rules, fieldType = "text", extra, type, ...restProps } = item

        return (
            <Form.Item
                extra={extra}
                label={label} name={name} rules={rules}>
                <Input
                    type={fieldType}
                    placeholder={item.placeholder || '请输入'} {...restProps} />
            </Form.Item>
        )
    },
    textarea: (item: IFormItem) => {
        const { label, name, rules, fieldType = "text", extra, type, ...restProps } = item

        return (
            <Form.Item
                extra={extra}
                label={label} name={name} rules={rules}>
                <TextArea
                    placeholder={item.placeholder || '请输入'} {...restProps} />
            </Form.Item>
        )
    },

    picker: (item: IFormItem, pickerValueRef, pickerKeys: RefSet) => {
        const { label, name, rules, ...restProps } = item
        return (
            <Form.Item
                label={label}
                name={name}
                rules={rules}
                trigger="onConfirm"
                onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                    // console.log(item.disabled, "item.disabled");

                    if (item.disabled) {
                        return;
                    }
                    if (!item.column || item.column.length === 0) {
                        Toast.show({
                            content: "数据为空",
                        });
                        return;
                    }
                    datePickerRef.current?.open();
                }}
                arrow={
                    <img
                        style={{ width: "14px" }}
                        src="//j1.58cdn.com.cn/jinrong/images/ems1687853594098c2f0a2857791.png"
                        alt=""
                    />
                }
            >
                <Picker
                    columns={formatPickerColumns(item.column) as PickerColumn[]}
                    {...(restProps as any)}
                >
                    {(value) => {
                        return (
                            <>
                                <Input
                                    readOnly
                                    value={
                                        formatSelect(
                                            value,
                                            item.name,
                                            pickerValueRef,
                                            item
                                        ).valueStr
                                    }
                                    placeholder={item.placeholder || "请选择"}
                                ></Input>
                            </>
                        );
                    }}
                </Picker>
            </Form.Item>
        );
    },
    datePicker: (item: IFormItem) => {
        const { label, name, rules, ...restProps } = item
        return (
            <Form.Item
                name={name}
                label={label}
                rules={rules}
                trigger="onConfirm"
                onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                    datePickerRef.current?.open();
                }}
                arrow={
                    <img
                        style={{ width: "14px" }}
                        src="//j1.58cdn.com.cn/jinrong/images/ems1687853594098c2f0a2857791.png"
                        alt=""
                    />
                }
            >
                <MyPicker {...restProps}></MyPicker>
            </Form.Item>
        );
    },
    cascader: (item: IFormItem, pickerValueRef) => {
        // console.log(item, '====!!!!====');
        const { label, name, rules, column: options, ...restProps } = item
        return (
            <Form.Item
                name={name}
                label={label}
                rules={rules}
                trigger='onConfirm'
                onClick={(e, datePickerRef: RefObject<DatePickerRef>) => {
                    datePickerRef.current?.open()
                }}
                arrow={
                    (<img style={{ width: '14px' }} src="//j1.58cdn.com.cn/jinrong/images/ems1687853594098c2f0a2857791.png" alt="" />)
                }
            >
                <Cascader
                    className='my_adm_cascader'
                    options={(item.options || options || []) as CascaderOption[]}
                    {...restProps as any}
                >
                    {value => {
                        // console.log(value, "---000")
                        return <>
                            <Input
                                readOnly
                                value={formatSelect(value, item.name, pickerValueRef, item).valueStr}
                                placeholder={item.placeholder || '请选择'}
                            ></Input>
                        </>
                    }}
                </Cascader>
            </Form.Item>
        )
    },
    customPicker: (item: IFormItem) => {
        const { label, name, rules, onClick, ...restProps } = item

        return (
            <Form.Item
                arrow={
                    (<img style={{ width: '14px' }} src="//j1.58cdn.com.cn/jinrong/images/ems1687853594098c2f0a2857791.png" />)
                }
                onClick={() => onClick && onClick()}
                label={label} name={name} rules={rules}
            >
                <Input readOnly placeholder={item.placeholder || '请输入'} {...restProps} />
            </Form.Item>
        )
    },
}


export default function BaseForm({ form, formColumn, disabled = false }: IBaseForm) {

    // pickerValueRef 用来存储picker value对应的name
    const pickerValueRef = useRef<AllType>({})
    // 回显数据时 将picker的基础类型 处理成数组格式 （picker和cascader只接受数组格式的value用来回显）
    // 因此这里存储picker类型对应item的name 回显时使用
    const pickerKeys = useRef<Set<string>>(new Set())
    getPickerKeys(formColumn, pickerKeys)
    const [innerForm] = Form.useForm();
    useImperativeHandle(
        form,
        () => ({
            ...innerForm,
            getFieldsValue: () => {
                // handlePickerValues
                const res = innerForm.getFieldsValue();
                return {
                    ...handlePickerValues(res, pickerKeys),
                    ...pickerValueRef.current
                }
            },
            setFieldsValue: (reshowObj: AllType) => {
                const newReshowObj = handlePickerReshowValues(reshowObj, pickerKeys);

                innerForm.setFieldsValue(newReshowObj)
            },
            validateFields: async () => {
                const res = await innerForm.validateFields();
                return {
                    ...handlePickerValues(res, pickerKeys),
                    ...pickerValueRef.current
                }
            }
        }),
    )
    return (
        <div className="form_container">
            <Form disabled={disabled} form={innerForm} layout='horizontal' mode='card'>
                {
                    formColumn && formColumn.map((item, index) => {
                        return (
                            < div className='form_container_box' key={index}>
                                <div className="f_title" >{item.title}</div>
                                {
                                    item.fields.map(field => {
                                        return (
                                            field.hidden ? null : <div key={field.name} className={field.align || "right"}>
                                                {formTypeMap[field.type](field, pickerValueRef, pickerKeys)}
                                            </div>

                                        )
                                    })
                                }

                            </div>
                        )
                    })
                }

            </Form>

        </div>
    )
}
