@import "~scss/mixins/px2rem";

.form_container {
    border-top: 1px solid transparent;
    --adm-color-danger: red;
    button,
    input,
    optgroup,
    select,
    textarea {
        font-family: PingFangSC-Medium;
    }
    &_box {
        background-color: #ffffff;
        // margin-top: rem(24);
        padding: 0 rem(24);
        border-radius: rem(16);
    }
    .adm-list-body {
        font-size: rem(28);
        color: #999999;
        border-radius: initial;
        // color:
        // red;
    }
    .adm-form-item-label {
        white-space: nowrap;
    }

    // 去除必填状态自带的 * 号
    .adm-form-item-required-asterisk {
        display: none;
    }
    .adm-form-item-feedback-error {
        font-size: rem(24);
    }
    .adm-list-item {
        padding-left: 0;
    }
    .adm-list-item-content {
        position: relative;
    }
    .place_holder {
        color: #999999;
        font-size: rem(28);
    }
    .f_title {
        color: #333333;
        font-size: rem(36);
        margin-bottom: rem(16);
        padding-top: rem(32);
        font-family: PingFangSC-Semibold;
    }
    .left {
        border-bottom: 0.5px solid #eeeeee;

        .adm-list-item-description {
            text-align: left;
        }
        .adm-form-item .adm-input {
            --text-align: left;
        }
        .adm-form-item-child-inner {
            text-align: left;
        }
    }
    .right {
        border-bottom: 0.5px solid #eeeeee;

        .adm-list-item-description {
            text-align: right;
        }
        .adm-input {
            --text-align: right;
        }
        .adm-form-item-child-inner {
            text-align: right;
        }
    }
    .right:last-child {
        border: none;
    }
    .left:last-child {
        border: none;
    }
    .adm-list-item-content {
        border-top: none;
        // border-bottom: 0.5px solid #eeeeee;
    }
    .adm-form-item.adm-form-item-horizontal .adm-list-item-content-prefix {
        padding: rem(30) 0px;
    }
    .adm-list-item-content-main {
        padding: rem(30) 0px;
    }
    .adm-form-item-label {
        // font-size: 16px;
        font-weight: 500;
        color: #666666;
    }
    .adm-input-element {
        color: #333333;
        font-size: rem(28);
    }
    .adm-input-element::placeholder {
        font-size: rem(28);
        color: #999999;
    }
    // disable的置灰状态
    .adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > * {
        opacity: 0.8;
        pointer-events: none;
    }
}

.adm-popup {
    --adm-color-background: #ffffff;
    --adm-font-size-6: 16px;
    --adm-font-size-7: 16px;
    --adm-font-size-8: 16px;
    --adm-font-size-9: 16px;

    .adm-cascader-header-button {
        color: #00c682;
    }
    .adm-picker-header-button {
        color: #00c682;
    }
}
.adm-form-item-child {
    color: #333333;
}
.adm-cascader {
    background-color: #ffffff;
}
.my_adm_cascader {
    .adm-tabs{
        --active-title-color: #00c682;
        --adm-color-primary:#00c682;
    }
}
