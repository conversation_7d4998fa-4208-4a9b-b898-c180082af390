import React from 'react';
import { Link } from 'react-router-dom';
import './index.scss';

export default class Component extends React.Component {
    render() {
        const { href, children, onClick, ...others } = this.props;
        //判断是否href
        const hrefState = href ? '1' : '0';
        //若onclick存在，则取onClick,否则取hrefState;
        const linkState = onClick ? '2' : hrefState;

        return linkState === '1' ? (
            <a {...others} href={href} className="link">
                {children}
            </a>
        ) : linkState === '2' ? (
            <div {...this.props} className="link"></div>
        ) : (
            <Link {...this.props} className="link" />
        );
    }
}
