import React, { useEffect, useMemo, useState, useRef } from 'react'
import { ImageUploader, ImageViewer } from 'antd-mobile-v5';
import "./index.scss"

const UploadComp = (props) => {
    const [visible, setVisible] = useState(false);
    const imgPreRef = useRef<any>(null);
    useEffect(() => {

    }, [props.value]);
    const previewValueArr = useMemo(() => {
        const previewUrl = props.value.map((item) => {
            return item.thumbnailUrl || item.url
        })
        return previewUrl || []
    }, [props.value])
    return <>
        <ImageUploader
            preview={false}
            onPreview={(a, b) => {
                console.log(a, b);
                setVisible(true);
                imgPreRef.current?.swipeTo(a, false);
                if (typeof props.onTogglePreviewCb === 'function') {
                    props.onTogglePreviewCb(true);
                }
            }}
            {...props}
        >
            {props.children}
        </ImageUploader>
        <ImageViewer.Multi
            ref={imgPreRef}
            images={previewValueArr}
            visible={visible}
            onClose={() => {
                setVisible(false);
                if (typeof props.onTogglePreviewCb === 'function') {
                    props.onTogglePreviewCb();
                }
            }}
        />

    </>

}

const WosUpload = (props) => {
    return (<div className='rem_upload'>
        <UploadComp {...props}></UploadComp>
    </div>);
};

export default WosUpload;