.protocol-dialog-common {
  .content {
    width: calc(100vw - 52px);
    box-sizing: border-box;
    padding: 12px 16px;
    background-color: #FFF;
    border-radius: 8px;
  }

  .title {
    font-size: 20px;
    font-weight: 400;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-bottom: 12px;

    img {
      position: absolute;
      right: 0;
    }
  }

  .context {
    max-height: 70vh;

    overflow: auto;

    img {
      display: block;
    }
  }

  .scale-context {
    transition: all 0.3s ease;
    transform-origin: left top;
  }

  .operation-iconList {
    text-align: center;
    padding-top: 12px;

    img+img {
      margin-left: 20px;
    }
  }
}