@import "~scss/mixins/px2rem";

.upload_image_container {
    --adm-color-white:#ffffff;
    &_item {
        margin-top: rem(24);
        background-color: #ffffff;
        padding: rem(32) rem(24);
        border-radius: rem(16);
        &_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            &_box {
                font-family: PingFangSC-Semibold;
                font-size: rem(36);
                font-weight: 700;
                color: rgba(51, 51, 51, 1);
                margin-bottom: rem(24);
                text-align: left;
                vertical-align: top;
                flex: 1;
                .isRequired {
                    margin-left: rem(12);
                    font-family: PingFangSC-Regular;
                    font-size: rem(24);
                    border: 1px solid rgba(0, 198, 130, 1);
                    border-radius: rem(4);
                    font-weight: 400;
                    padding: 0 rem(8);
                    line-height: rem(40);
                    vertical-align: top;
                    color: rgba(0, 198, 130, 1);
                }
                // justify-content: flex-start;
            }
            &_extra {
                max-width: rem(400);
            }
        }
        .describtion {
            font-family: PingFangSC-Regular;
            font-size: rem(24);
            font-weight: 400;
            color: #666666;
            line-height: rem(33);
            text-align: left;
            margin-bottom: rem(24);
        }

        .modifySuggestion {
            background: linear-gradient(90deg, rgba(255, 251, 236, 1) 0%,rgba(255, 253, 249, 1) 100%);
            border-radius: rem(8);
            padding: rem(16) rem(12);
            margin-bottom: rem(24);
            font-family: PingFangSC-Regular;
            font-size: rem(24);
            font-weight: 400;
            line-height: rem(33);
            color: #D89527;
            text-align: left;
        }
    }
}
