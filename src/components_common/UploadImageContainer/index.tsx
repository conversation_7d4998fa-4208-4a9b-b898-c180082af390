/*
 * @Author: zhangwu01 <EMAIL>
 * @Date: 2024-01-15 11:23:58
 * @LastEditors: zhangwu01 <EMAIL>
 * @LastEditTime: 2024-10-23 09:55:22
 * @Description: 图片container 
 * 
 */
import React, { ReactNode, forwardRef, useRef, useImperativeHandle, useMemo, useEffect } from 'react'
import UploadImage from 'commons/UploadImage';
import "./index.scss";



export interface IImageItem {
    title: string | any,
    formKey: string | number,
    isRequired?: boolean,
    maxCount?: number,
    disabled?: boolean;
    describtion?: ReactNode,
    extra?: ReactNode,
    hidden?: boolean
    modifySuggestion?: ReactNode
}
interface IDataSource {
    [formKey: string]: { url: string }[]
}
interface IProps {
    imagList: IImageItem[];
    dataSource: IDataSource;
    disabled?: boolean;
    ref?: any;
    multiple?: boolean;
    onTogglePreviewCb?: Function;
}
export interface IImageRef {
    getImageValues: any,
    validateImages: () => IImageItem[];

}
// 过滤掉属性为hidden的字段
function filterHiddenItem(imagList: IImageItem[]) {
    const filterImageList = imagList.filter(item => {
        return item.hidden !== true
    });

    return filterImageList;

}
const UploadImageContainer = forwardRef<IImageRef, IProps>((
    { imagList = [], dataSource = [], disabled = false, onTogglePreviewCb, multiple = false },
    ref,
) => {


    // 存储value的ref
    const picValuesRef = useRef<{ [picName: string]: string[] }>({});
    // 存储当前页面渲染的（hidden为false） 图片的key值 用来去除当imglist变化时 旧值还在的问题
    const realImageKeysRef = useRef<string[]>([])

    const realImageList = useMemo(() => {
        // 防止formList变化时 picValuesRef还存储这上一次的值

        return filterHiddenItem(imagList)
    }, [imagList]);
    console.log(
        realImageList, "realImageList"
    );
    useEffect(() => {
        realImageKeysRef.current = realImageList.map((item) => {
            return item.formKey + "";
        });
    }, [realImageList])

    console.log(realImageKeysRef, "realImageKeys");

    const onChange = (e) => {
        if (e) {
            picValuesRef.current = { ...picValuesRef.current, ...e };

        }

    }
    /**
     * @description:  获取当前已经上传图片的url
     * @return {*}
     */
    // 获取picvalue
    const getImageValues = () => {
        const realImageKeys = realImageKeysRef.current;
        const picValues = picValuesRef.current;
        const finalValue = {}
        realImageKeys.forEach((item) => {
            finalValue[item] = picValues[item]
        })
        return finalValue;
    }

    // 是否通过校验
    const valideValue = (formKey) => {
        const value = picValuesRef.current[formKey];

        if (Array.isArray(value) && value.length > 0) {
            return true;
        }
        return false

    }
    /**
     * @description: 校验图片 只校验是否必填
     * @return {*} 
     * 被校验住的 都会返回当前的 imsList item的所有信息
     */
    const validateImages = () => {
        // 目前只校验是否必填
        const requiredFormItem = realImageList.reduce<IImageItem[]>((prev, cur, i) => {

            if (cur.isRequired && !valideValue(cur.formKey)) {
                prev.push(cur)
            }
            return prev
        }, [])
        return requiredFormItem;
    }
    useImperativeHandle(ref, () => {
        return {
            getImageValues: getImageValues,
            validateImages: validateImages
        };
    }, [realImageList]);

    return (
        <div className='upload_image_container'>
            {
                realImageList.map((item) => {
                    const { formKey, disabled: itemDisabled, ...otherItem } = item
                    return (
                        <div className="upload_image_container_item" key={formKey}>
                            <div className="upload_image_container_item_title">
                                <div className='upload_image_container_item_title_box'>
                                    {/* 标题 */}
                                    <span>{item.title}</span>
                                    {/* 是否必传 */}
                                    {
                                        item.isRequired ? <span className='isRequired'>
                                            必传
                                        </span> : null
                                    }
                                </div>
                                {/* 标题最右测的自定义内容 */}
                                {
                                    <div className='upload_image_container_item_title_extra'>
                                        {item.extra}
                                    </div>
                                }
                            </div>
                            {/* 标题下方的描述 */}
                            {item.describtion &&
                                <div className="describtion">
                                    {item.describtion}
                                </div>
                            }
                            {/* 修改意见 */}
                            {item.modifySuggestion &&
                                <div className="modifySuggestion">
                                    {item.modifySuggestion}
                                </div>
                            }

                            <UploadImage
                                multiple={multiple}
                                value={dataSource[formKey] || []}
                                disabled={disabled || itemDisabled}
                                deletable={!(disabled || itemDisabled)}
                                formKey={formKey}
                                onChange={onChange}
                                onTogglePreviewCb={onTogglePreviewCb}
                                {...otherItem}
                            />
                        </div>
                    )
                })
            }

        </div>
    )
}
)
export default React.memo(UploadImageContainer);