/**
 * 404组件
*/
import React, { FC } from 'react';
import './index.scss';

interface IProps {
    description?: React.ReactNode;
}

const NotFound: FC<IProps> = ({ description = '此页面不存在' }) => {
    return (
        <section className="notfound-page">
            <img
                className="img"
                src="//j1.58cdn.com.cn/jinrong/images/ems15785510921655658ec70749f9.png"
                alt=""
            />
            <div className="desc">{description}</div>
        </section>
    );
};

export default NotFound;
