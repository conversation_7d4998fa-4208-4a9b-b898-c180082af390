@import "~scss/mixins/px2rem";

.in_purchase_tag {
    display: flex;
    background-color: #ffffff;
    font-family: PingFangSC-Regular;
    flex-wrap: wrap;
    font-size: rem(28);
    &_item {
        height: rem(80);
        min-width: rem(208);
        box-sizing: border-box;
        background-color: rgba(247, 247, 247, 1);
        border-radius: rem(54);
        padding: 0 rem(28);
        margin: rem(16) 0;
        margin-right: rem(20);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &_name {
        }
        &_describe {
            margin-top: rem(6);

            font-size: rem(20);
        }
    }
    .active {
        background-color: rgba(0, 198, 130, 0.16);
        font-family: PingFangSC-Medium;
        color: rgba(0, 198, 130, 1);
        font-weight: 500;
    }
    .disabled {
        color: #999999;
    }
}
