import React, { useEffect } from 'react';
import "./index.scss";
import classNames from 'classnames';


export interface ITagItem {
    name?: string;
    code?: string | number;
    disabled?: boolean;
    [propName: string]: any
}
interface IProps {
    activeKey: string | number | undefined;
    onChange: (code: string | number, item: ITagItem | void) => void;
    tagList: ITagItem[];
    disabledText?: string;
    fieldsName?: {
        name: string;
        code: string;
    }
}


const Tags: React.FC<IProps> = ({
    activeKey,
    onChange,
    tagList = [],
    fieldsName = { name: "name", code: "code" },
    disabledText = "已约满",
}) => {
    const name = fieldsName.name;
    const code = fieldsName.code;
    const innerChange = (innerCode, row: ITagItem = {}) => {
        const { disabled = false } = row;
        if (disabled) return;
        const item = tagList.find((item) => item[code] === innerCode);
        if (innerCode !== activeKey) {
            onChange && onChange(innerCode, item);
        }
    };

    useEffect(() => {
        if (tagList.length <= 0 || !activeKey) {
            return;
        }
        innerChange(activeKey);
    }, [activeKey, tagList]);

    return (
        <div className="in_purchase_tag">
            {tagList.map((item) => {
                return (
                    <div
                        key={item[code]}
                        className={classNames("in_purchase_tag_item", {
                            active: item[code] === activeKey,
                            disabled: item.disabled === true,
                        })}
                        onClick={() => innerChange(item[code], item)}
                    >
                        <span className="in_purchase_tag_item_name">
                            {item[name]}
                        </span>
                        {item.disabled ? (
                            <span className="in_purchase_tag_item_describe">
                                {disabledText}
                            </span>
                        ) : null}
                    </div>
                );
            })}
        </div>
    );
};

export default Tags;