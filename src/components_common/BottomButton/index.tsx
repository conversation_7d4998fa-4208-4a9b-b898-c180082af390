import React, { FC, ReactNode } from 'react';
import { Button } from 'antd-mobile-v5';
import "./index.scss";

interface IProps {
    children: ReactNode;
    extra?: ReactNode;
    onClick: () => void,
    [key: string]: any
}
const BottomButton: FC<IProps> = ({ children = "提交", onClick = () => { }, extra = "", ...props }) => {
    return (
        <div className='bottom_button'>
            {extra}
            <Button onClick={onClick} {...props}>{children}</Button>
        </div>
    )
}
export default BottomButton;