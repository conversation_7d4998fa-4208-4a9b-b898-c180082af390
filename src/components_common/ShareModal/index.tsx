import React from "react";
import ReactDOM from 'react-dom';

import "./index.scss";

const ShareModal = (props) => {
    const root = props.root;
    const modalRoot = props.el;

    const onRemoveModal = () => {
        root.removeChild(modalRoot);
    }

    return (
        <div className="modal">
            <div className="modal-root" onClick={onRemoveModal} />
            <img
                className="instruct"
                src="//j1.58cdn.com.cn/jinrong/images/ems16055285932048d96d2c760683.png" alt="点击右上角分享"
            />
        </div>

    )
}

const renderDom = () => {
    let el = document.createElement('div');
    el.setAttribute('id', 'modal-root');

    let root = document.getElementsByTagName("body")[0];
    root.appendChild(el);

    ReactDOM.render(
        <ShareModal el={el} root={root}/>,
        el,
    )
}

export {
    renderDom,
}
