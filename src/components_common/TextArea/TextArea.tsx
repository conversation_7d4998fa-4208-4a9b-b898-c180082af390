import React, { useEffect, useRef } from 'react';
import "./index.scss";

function TextArea({
    value = "",
    placeholder = "请输入",
    onChange = (e) => { },
    ...restProps
}) {
    const textareaRef = useRef<any>();
    useEffect(() => {
        // document.getElementById('myTextarea').addEventListener('input', function () {
        //     this.style.height = 'auto';
        //     this.style.height = this.scrollHeight + 'px';
        // });
    }, [])
    return (
        <div>
            <textarea
                // id="myTextarea"
                ref={textareaRef}
                value={value}
                placeholder={placeholder}
                onChange={(e) => {
                    textareaRef.current.style.height = 'auto';
                    textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
                    onChange(e.target.value)
                }}

                className='dbp_text_area_element' wrap="soft"
                {...restProps}
            />
        </div>
    )
}

export default TextArea;