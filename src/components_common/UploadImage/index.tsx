import WosUpload from "commons/WosUpload";
import Compressor from 'compressorjs';
import { uploadPic } from 'tools/wos';
import React, { useState, useEffect, FC, useRef } from 'react'
import { Toast } from "antd-mobile";
import { ImageUploadItem, ImageUploaderProps, UploadTask } from 'antd-mobile-v5/es/components/image-uploader';
import { AddOutline } from "antd-mobile-icons";
import "./index.scss";
// import { locStorage } from '@sqb/utility';
import heic2any from 'heic2any';

// 判断当前是不是ios15.5
const isIOS15_5 = () => {
    const ua = navigator.userAgent;
    const ios = !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    const version = ua.match(/OS (\d+)_(\d+)_?(\d+)?/);

    if (ios && version) {
        const [, major, minor, patch] = version;
        console.log(typeof major, minor, 1, "major,minor");

        return (major === '15' && minor === '5') || (major === '15' && minor === '4');
    }
    return false
}
// ImageUploaderProps
async function convertHeicToBlob(file): Promise<{
    blob: Blob | Blob[];
    url: string;
} | undefined> {
    try {
        console.time("convertHeicToBlob");
        // 将HEIC文件转换为JPEG Blob
        const blob = await heic2any({ blob: file, toType: 'image/jpeg' });

        // 创建URL以便预览
        const url = URL.createObjectURL(blob as any);
        console.timeEnd("convertHeicToBlob");
        // 记得释放URL对象以避免内存泄漏
        return {
            url,
            blob
        };
    } catch (error) {
        console.error('转换失败:', error);
        return void 0;
    }
}

export type IImageUploaderProps = Omit<ImageUploaderProps & {
    formKey: string | number;
    onChange?: (value: { [name: string]: ImageUploadItem[] }) => void,
    title?: string;
    disabled: boolean
}, "upload">
const UploadImage: FC<IImageUploaderProps> = ({ onChange, value = [], formKey = "", disabled, maxCount = 2, ...props }) => {

    const [fileList, setFileList] = useState<ImageUploadItem[]>([]);
    // 存储上传图片name的数组，从而确定当前 第几个loading上传进度
    const nameArrRef = useRef<string[]>([]);

    useEffect(() => {
        onChange && onChange({ [formKey]: fileList })
    }, [fileList, value])

    useEffect(() => {
        setFileList(value);
    }, [value]);
    // 进度文案设置
    const uploadText = (progress: number = 0, loadingDom) => {
        try {
            const progressInfo = (progress * 100).toFixed() + "%"
            // const ele = document.getElementsByClassName("adm-image-uploader-cell-mask-message");
            // console.log(ele);

            if (loadingDom) loadingDom.textContent = `上传中${progressInfo}`
        } catch (e) {
            console.log(e);

        }

    }
    //上传方法
    const upload = async (file) => {
        // promise fail 失败  pendding loading resolve 成功
        let newFile = file;
        let filename = `${(+new Date()).toString()}-${file.name.slice(-10)}`;
        const ele = document.getElementsByClassName("adm-image-uploader-cell-mask-message");
        nameArrRef.current.push(filename);
        const index = nameArrRef.current.findIndex(name => filename === name);
        //文件后缀
        const suffix = file.name.slice(file.name.lastIndexOf('.'));
        console.log(file.size / 1024 / 1024, file.name, suffix, "filename");
        try {
            let blobUrl: any = URL.createObjectURL(file);
            console.log(blobUrl, file.type, "blobUrl");
            if (file && file.type === 'image/heic') {
                // 改为multi模式后 此部分代码暂时不会触发
                const heicInfo = await convertHeicToBlob(file);
                blobUrl = heicInfo?.url;
                newFile = heicInfo?.blob;
            }

            const loadingDom = ele[index];
            const res: any = await uploadPic(filename, newFile, undefined, (progress) => {
                // console.log(progress, "progressprogress");

                uploadText(progress, loadingDom);
            });
            // 处理完删除
            const deleteIndex = nameArrRef.current.findIndex(name => filename === name);
            // 将已经上传完的filename删除
            nameArrRef.current.splice(deleteIndex, 1);
            let url = res.data.url;
            console.log(nameArrRef.current, "  nameArrRef.current");

            // if (file && file.type === 'image/heic') {
            //     const wosToken = locStorage.get("wos_token_ticket");
            //     // 安卓手机 需要先wimages=/format/jpeg|/quality/50 转化为jpeg 在压缩50%
            //     blobUrl = `${url}?token=${wosToken}&wimages=/format/jpeg|/quality/20`;
            //     url = `${url}?wimages=/format/jpeg|/quality/50`
            // }

            if (res.code === 0) {
                return {
                    url: url,
                    thumbnailUrl: blobUrl,
                }
            }
        } catch (e) {
            nameArrRef.current = [];
        }
        return Promise.reject();

    }
    //删除图片
    const onDelete = (type) => {
        console.log(type);

    }



    // 压缩处理文件
    const processTheFile = async (file) => {
        let filename = `${(+new Date()).toString()}-${file.name.slice(-10)}`;
        //文件名去除特殊字符及空格
        filename = filename.replace(/[&\/\\#,+()&^$~%'":*?<>{}|]/g, '').replace(/\s+/g, '');
        //图片压缩，图片大于500kb,压缩到500kb，Blob类型
        try {

            // const blobRes = await imageConversion.compressAccurately(file, {
            //     size: 700,
            //     scale: 0.5,
            // });

            const newFile = await new Promise((resolve, reject) => {
                new Compressor(file, {
                    quality: 0.6, // 压缩质量
                    success(result) {
                        resolve(result); // 压缩成功，返回压缩后的文件
                    },
                    error(err) {
                        resolve(file); // 压缩失败，抛出错误
                    },
                });
            });
            return { filename, newFile };
        } catch (e) {

            console.log("压缩失败");

            // 如果压缩失败 则直接用原图
            return { filename, newFile: file }

        }
    }



    //文件读取前的回调函数，返回 null 可终止文件读取
    const beforeUpload = async (ocrType, file) => {
        // if (file && file.type === 'image/heic') {
        //     Toast.show("不支持该格式图片", 1)
        //     return null
        // }
        Toast.loading(
            "图片上传中", 0
        )
        // if (isIOS15_5()) {
        //     // 先做个临时处理 ios15 压缩时页面会卡死
        //     return file;
        // }
        console.log(file.size / 1024 / 1024, "old");

        const { newFile } = await processTheFile(file);
        return newFile;
    }
    // Toast.hide();
    const onUploadQueueChange = (e: UploadTask[]) => {
        if (e.length === 0) return;

        // 当队列中没有pendding时 取消弹层Toast.hide();
        const penddingStatus = e.find(taskItem => taskItem.status === "pending");
        if (!penddingStatus) {
            // 说明此时状态全部是完成状态（失败或成功）
            Toast.hide();
        }
    }

    return (
        <div className="settlement_image_upload">
            <WosUpload
                value={fileList}
                onChange={setFileList}
                upload={(file) => upload(file)}
                beforeUpload={(file) => beforeUpload('formKey', file)}
                maxCount={disabled ? 1 : maxCount}
                onDelete={(e) => onDelete(e)}
                onUploadQueueChange={onUploadQueueChange}
                {...props}
            >
                <div className="add_pic_btn">
                    <div className="add_pic_btn_icon">
                        <AddOutline color='#ffffff' fontSize={24} />
                    </div>
                    上传照片
                </div>
            </WosUpload>

        </div>
    )
}

export default UploadImage;