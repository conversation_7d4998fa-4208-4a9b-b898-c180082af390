@import "~scss/mixins/px2rem";

.settlement_image_upload {
    box-sizing: border-box;
    .add_pic_btn {
        box-sizing: border-box;
        border: rem(1) dashed rgba(153, 153, 153, 1);
        width: rem(202);
        height: rem(202);
        border-radius: rem(8);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-size: rem(24);
        color: #666666;
        &_icon {
            background-color: #cdcdcd;
            width: rem(64);
            height: rem(64);
            border-radius: 50%;
            margin-bottom: rem(18);
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    .adm-image-uploader-cell-fail {
        border: none;
        background-color: #6e6e6c;
        background-image: url(//wos.58cdn.com.cn/cDazYxWcDHJ/picasso/4pq6gtp3__w192_h170.png);
        background-size: 50%;
        background-repeat: no-repeat;
        background-position: 50%;
        .adm-image-uploader-cell-image {
            opacity: 0.2;
        }
    }
    .adm-space-item:nth-of-type(3n) {
        margin-right: 0;
    }
    .adm-image-uploader-cell-mask {
        background-color: rgba(51, 51, 51, 0.5);
        .adm-image-uploader-cell-loading {
            .adm-spin-loading {
                --size: 22px;
            }
            .adm-image-uploader-cell-mask-message {
                font-size: rem(24);
            }
        }
    }
}
.adm-image-viewer-slides-inner > * {
    margin-right: 0px;
}
